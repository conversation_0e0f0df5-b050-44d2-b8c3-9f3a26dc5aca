<?php

namespace app\api\controller\vppz;


use app\common\controller\Api;

use think\Config;
use think\Cache;
use think\Db;
use think\Lang;
use think\Loader;

use \app\admin\model\vppz\App as AppModel;
use \app\admin\model\vppz\User as UserModel;
use \app\admin\model\vppz\Money as MoneyModel;


class AppBase extends Api

{

    protected $noNeedLogin = ['*'];

    protected $noNeedRight = ['*'];


	protected $AppModel;
	protected $UserModel;
	protected $MoneyModel;

    protected $app_id = 1;

	protected $_cfg = null;// 当前app，用cfg作为名称方便理解

	protected $user_id = 1;
	protected $_mine = null;// 当前用户user，用mine作为名称方便统一


    public function _initialize()

    {

		/**
		if(isset($_SERVER['HTTP_ORIGIN'])){
			header('Access-Control-Expose-Headers:__token__');
		}
		check_cors_request();
		if(!isset($_COOKIE['PHPSESSID'])){
			Config::set('session.id',$this->request->server('ssid'));
		}
		**/
		
		//手动加载config语言类
		$this->loadlang('vppz.config');

        parent::_initialize();

		
		$this->AppModel = new AppModel;
		$this->UserModel = new UserModel;
		
		// 获取当前应用信息即配置
		$this->app_id = input('appid');
		if(empty($this->app_id)){
			$this->error('缺少参数appid');
		}
		$this->_cfg = $this->AppModel->find($this->app_id);
		if(empty($this->_cfg)){
			$this->error('当前appid应用不存在');
		}

		// 附加显示文字配置
		$dict=[
			'avatar' => __('Avatar'),
			'id_name' => __('Id_name'),
			'id_num' => __('Id_num'),
			'id_pic' => __('Id_pic'),
			'outcash_wx' => __('Outcash_wx'),
			'outcash_ali' => __('Outcash_ali'),
			'outcash_bank' => __('Outcash_bank'),
			'outcash_card' => __('outcash_card'),
			'realname' => __('Realname'),
		];
		$this->_cfg['dict']=$dict;

		// 获取当前用户（登录和会话检测行为除外）
		if(!in_array($this->request->action(),['wxlogin','sessioncheck'])){
			$sessionid=input('ssid');
			if(empty($sessionid)){
				$this->error('会话无效，请您重新发起登录请求。');
			}
			$this->user_id = Cache::get($sessionid);
			//$this->user_id = session($sessionid);
			if(empty($this->user_id)){
				$this->error('会话失效，请重新进入小程序。');
			}
			$this->_mine = $this->UserModel->find($this->user_id);
			if(empty($this->_mine)){
				$this->error('您的用户信息不存在，请先登录。');
			}
			// 加密我的ID
			$this->_mine['_id']=\addons\vppz\library\Vpower::pencode($this->_mine['id']);
		}


    }


	// 记录金额变化
	/**
	'who'=>'city',
	'who_id'=>$city['id'],
	'uid'=>$city['uid'],		// 城市负责人uid，可能为空，只用作记录，收入归城市，不归人
	'money'=> $tax_city_fee,
	'biz'=>'order',
	'biz_id'=>$order['id'],
	'biz_type'=>'city',
	'biz_name'=>$order['service_name'],
	'remark'=>'订单总价：'.$order['amount'].'元'
	***/
	protected function recordMoney($money){
		/**
		if(empty($this->MoneyModel)){
			$this->MoneyModel = new MoneyModel;
		}
		**/

		$money['app_id']=$this->app_id;
		$money['createtime']=time();

		Db::name('vppz_money')->insert($money);

		//$this->MoneyModel->save($money);
	}

    /**
	 * 重写父类函数,将api语言包加载路径转为admin模块中的语言包，实现前后端统一语言定义
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        $name = Loader::parseName($name);
        $name = preg_match("/^([a-zA-Z0-9_\.\/]+)\$/i", $name) ? $name : 'index';
        $lang = $this->request->langset();
        $lang = preg_match("/^([a-zA-Z\-_]{2,10})\$/i", $lang) ? $lang : 'zh-cn';
        Lang::load(APP_PATH . 'admin' . '/lang/' . $lang . '/' . str_replace('.', '/', $name) . '.php');
    }
}
define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	$.validator.config({
		rules: {
			taxsum: function (element) {
				if (element.value < 0) {
					return '不能为负数';
				}
				if((parseInt($('#c-profit').val())+parseInt($('#c-tax_seller').val())+parseInt($('#c-tax_master').val())+parseInt($('#c-tax_plat').val()))>100){
					return '收益之和不能超过100';
				}
				return true
			}
		}
	});

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/area/index' + location.search,
                    add_url: 'vppz/area/add',
                    edit_url: 'vppz/area/edit',
                    del_url: 'vppz/area/del',
                    multi_url: 'vppz/area/multi',
                    import_url: 'vppz/area/import',
                    table: 'vppz_area',
                }
            });

            var table = $("#table");
			
			var formatter_region=function(value,row,index){
				return row.province+'/'+row.city+(row.district?('/'+row.district):'');
			};

			var formatter_percent=function(value,row,index){
				return value+'%';
			};
			var formatter_percent_area=function(value,row,index){
				return (100-row.profit-row.tax_seller-row.tax_master-row.tax_plat)+'%'+'~'+(100-row.profit-row.tax_plat)+'%';
			};

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: '运营区域', operate: 'LIKE'},
						{field: 'region', title: '覆盖范围', operate: 'LIKE',formatter:formatter_region,searchable:false},
                        {field: 'odmode', title: '接单模式', searchList: {"0":__('Odmode 0'),"10":__('Odmode 10')}, formatter: Table.api.formatter.normal},
                        {field: 'profit', title: '陪护师收益', operate:'BETWEEN',formatter:formatter_percent},
						{field: 'tax_seller', title: '推广者收益', operate:'BETWEEN',formatter:formatter_percent},
						{field: 'tax_master', title: '团队长收益', operate:'BETWEEN',formatter:formatter_percent},
                        {field: 'tax_plat', title: '总部收益', operate:'BETWEEN',formatter:formatter_percent},
						{field: 'tax_area', title: '运营区收益', operate:'BETWEEN',formatter:formatter_percent_area},
                        //{field: 'tax_seller', title: __('Tax_seller'), operate:'BETWEEN'},
						{field: 'seller_reg', title: '推广者开通', searchList: {"1":__('Seller_reg 1'),"2":__('Seller_reg 2'),"3":__('Seller_reg 3')}, formatter: Table.api.formatter.normal},
						{field: 'seller_bind', title: '推广时效',searchable:false},
                        {field: 'staff_reg', title: '陪护师注册', searchList: {"1":__('Staff_reg 1'),"2":__('Staff_reg 2'),"3":__('Staff_reg 3'),"4":__('Staff_reg 4')}, formatter: Table.api.formatter.normal},
                        {field: 'staff_team', title: '陪护师团队', searchList: {"1":__('Staff_team 1'),"2":__('Staff_team 2')}, formatter: Table.api.formatter.normal},
						{field: 'staff_card', title: '陪护师名片', searchList: {"0":__('Staff_card 0'),"1":__('Staff_card 1')}, formatter: Table.api.formatter.normal},
                        {field: 'use_switch', title: '启用', table: table, formatter: Table.api.formatter.toggle,searchable:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
                        //{field: 'users', title: '区域用户',searchable:false},
                        {field: 'plat_in', title: '区域营业额', operate:'BETWEEN'},
						{field: 'money_in', title: '区域收入', operate:'BETWEEN'},
						{field: 'plat_profit', title:'贡献总部', operate:'BETWEEN'},
                        {field: 'money', title: '待结算', operate:'BETWEEN'},
                        {field: 'money_outcash', title: '已结算', operate:'BETWEEN'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
							
							buttons: [{
                                    name: 'admins',
                                    title: '管理员',
									text: '管理员',
                                    classname: 'btn btn-xs btn-success btn-dialog',
                                    icon: 'fa fa-user',
                                    url: 'vppz/area_admin/index',
                                    //callback: function (data) {
                                    //    Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    //}
                                },{
                                    name: 'settle',
                                    title: '结算',
									text: '结算',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-jpy',
                                    url: 'vppz/area/settle'
                                }]}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'vppz/area/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), align: 'left'},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '130px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'vppz/area/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'vppz/area/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
		settle: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

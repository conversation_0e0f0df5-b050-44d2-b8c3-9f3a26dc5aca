define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/user/index' + location.search,
                    add_url: 'vppz/user/add',
                    edit_url: 'vppz/user/edit',
                    //del_url: 'vppz/user/del',
                    multi_url: 'vppz/user/multi',
                    import_url: 'vppz/user/import',
                    table: 'vppz_user',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'area.id', title:'所属运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false},
						{field: 'area.name', title:'所属运营区', operate: 'LIKE',searchable:false},
						{field: 'avatar', title: '用户', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
                        {field: 'nickname', title: '昵称', operate: 'LIKE'},
                        {field: 'mobile', title: '手机号', operate: 'LIKE'},
                        {field: 'openid', title: __('Openid'), operate: 'LIKE',visible: false,searchable:false},
                        //{field: 'province', title: __('Province'), operate: 'LIKE'},
                        //{field: 'city', title: __('City'), operate: 'LIKE'},
                        //{field: 'district', title: __('District'), operate: 'LIKE'},
						{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},

                        {field: 'orders', title: '下单数', operate:'BETWEEN'},
                        {field: 'orders_cancel', title:'取消数', operate:'BETWEEN'},
						{field: 'orders_done', title:'完成数', operate:'BETWEEN'},
                        {field: 'expends', title: '消费金额', operate:'BETWEEN'},
                       
                        //{field: 'invites', title: '邀请数', operate:'BETWEEN'},
						{field: 'seller_switch', title: '推广者', table: table, formatter: Table.api.formatter.toggle,searchList: {"0":'普通用户',"1":'推广者'}},
                        {field: 'sells', title: '推广单数', operate:'BETWEEN'},
                        {field: 'sell_money', title: '推广账户', operate:'BETWEEN'},
                        {field: 'sell_income', title: '推广收入', operate:'BETWEEN'},
                        {field: 'sell_outcash', title: '推广提现', operate:'BETWEEN'},
						//{field: 'admin_switch', title: '管理员', table: table, formatter: Table.api.formatter.toggle,searchList: {"0":'普通用户',"1":'管理员'}},
                        {field: 'black_switch', title: '黑名单', table: table, formatter: Table.api.formatter.toggle,color:'danger',searchList: {"0":'正常',"1":'黑名单'}},
						{field: 'remark', title: '备注', operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

<style>
	.text-label{padding-top:0 !important;}
</style>
<form id="settle-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="alert alert-info-light" style="margin-bottom:10px;">
        <b>结算提示</b><br>
		该功能用于记录运营区的收益结算扣除余额，请确保已经通过转账或其他方式给运营区合伙人结算相应金额
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">运营区名称:</label>
        <div class="col-xs-12 col-sm-8">
            {$row.name|htmlentities}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">待结算余额:</label>
        <div class="col-xs-12 col-sm-8">
            {$row.money|htmlentities}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">结算金额:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[outcash]" type="text" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">结算备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control " rows="5" name="row[remark]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

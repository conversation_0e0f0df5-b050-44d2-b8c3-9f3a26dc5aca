<!--@formatter:off-->
{if "[type]" == 'email'}
    <input type="text" name="captcha" class="form-control" data-rule="required;length({$Think.config.captcha.length});digits;remote({:url('api/validate/check_ems_correct')}, event=[event], email:#email)" />
    <span class="input-group-btn" style="padding:0;border:none;">
        <a href="javascript:;" class="btn btn-info btn-captcha" data-url="{:url('api/ems/send')}" data-type="email" data-event="[event]">发送验证码</a>
    </span>
{elseif "[type]" == 'mobile'/}
    <input type="text" name="captcha" class="form-control" data-rule="required;length({$Think.config.captcha.length});digits;remote({:url('api/validate/check_sms_correct')}, event=[event], mobile:#mobile)" />
    <span class="input-group-btn" style="padding:0;border:none;">
        <a href="javascript:;" class="btn btn-info btn-captcha" data-url="{:url('api/sms/send')}" data-type="mobile" data-event="[event]">发送验证码</a>
    </span>
{elseif "[type]" == 'wechat'/}
    {if get_addon_info('wechat')}
        <input type="text" name="captcha" class="form-control" data-rule="required;length({$Think.config.captcha.length});remote({:addon_url('wechat/captcha/check')}, event=[event])" />
        <span class="input-group-btn" style="padding:0;border:none;">
            <a href="javascript:;" class="btn btn-info btn-captcha" data-url="{:addon_url('wechat/captcha/send')}" data-type="wechat" data-event="[event]">获取验证码</a>
        </span>
    {else/}
        请在后台插件管理中安装《微信管理插件》
    {/if}
{elseif "[type]" == 'text' /}
    <input type="text" name="captcha" class="form-control" data-rule="required;length({$Think.config.captcha.length})" />
    <span class="input-group-btn" style="padding:0;border:none;">
        <img src="{:captcha_src()}" width="100" height="32" onclick="this.src = '{:captcha_src()}?r=' + Math.random();"/>
    </span>
{/if}
<!--@formatter:on-->

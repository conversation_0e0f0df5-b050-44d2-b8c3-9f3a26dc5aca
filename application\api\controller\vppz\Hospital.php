<?php
namespace app\api\controller\vppz;


use think\Cache;


use \app\admin\model\vppz\Hospital as HospitalModel;
use \app\admin\model\vppz\Service as ServiceModel;
use \app\admin\model\vppz\HospitalService as HospitalServiceModel;

use \addons\vppz\library\Vpower;

class Hospital extends App
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize(){
        parent::_initialize();
		

		$this->HospitalModel = new HospitalModel;
		$this->ServiceModel = new ServiceModel;
		$this->HospitalServiceModel = new HospitalServiceModel;
    }


    /**
     * 首页
     */
    public function index(){
		$area=$this->_area;


		$hid = input('hid');

		// 获取医院
		$hospital = $this->HospitalModel->where(['app_id'=>$this->app_id])->find($hid);
		//$hospital = pdo_fetch('SELECT * FROM ' .tablename('vp_pz_hospital') . ' where  uniacid = :uniacid AND city_id=:city_id AND id=:id AND status=1 AND is_del=0 ', array(':uniacid' => $_W['uniacid'],':city_id'=>$city['id'],':id'=>$hid));
		if(empty($hospital)){
			$this->error('该医院尚未开通');
		}
		if($hospital['use_switch']!=1){
			$this->error('该医院尚未开通相关服务');
		}

		//$hospital['avatar']=VP_IMAGE_URL($hospital['avatar']);
		//$hospital['service']=iunserializer($hospital['service']);
		$hss = $this->HospitalServiceModel->field('service_id,price,use_switch')->where(['app_id'=>$this->app_id,'area_id'=>$area['id'],'hospital_id'=>$hospital['id']])->select();

		// 获取服务
		//$services = pdo_fetchall('SELECT id,code,name,logo,intro,price FROM ' .tablename('vp_pz_service') . ' where  uniacid = :uniacid AND status=1 AND is_del=0 order by sort desc ', array(':uniacid' => $_W['uniacid']));
		$services = $this->ServiceModel->field('id,code,stype,name,logo_image,icon_image,intro,price,priceo')->where(['app_id'=>$this->app_id,'use_switch'=>1])->order('weigh', 'desc')->select();

		// 根据医院设置，覆盖服务
		for($i=0;$i<count($services);$i++){
			for($j=0;$j<count($hss);$j++){
				if($services[$i]['id']==$hss[$j]['service_id']){
					//$_service=$hospital['service'][$services[$i]['id']];
					$services[$i]['use_switch']=$hss[$j]['use_switch'];
					$services[$i]['price']=$hss[$j]['price'];
					//$services[$i]['logo']=VP_IMAGE_URL($services[$i]['logo']);
				}
			}
		}
		
		$this->success('',array(
			'now'=>time(),
			'hospital'=>$hospital,
			'services'=>$services
		));
    }


}
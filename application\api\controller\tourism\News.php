<?php

namespace app\api\controller\tourism;

use app\common\controller\Api;

/**
 * 旅游资讯接口
 */
class News extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 资讯列表
     */
    public function index()
    {
        $categoryId = $this->request->get('category_id/d', 0);
        $where = ['status' => 'normal'];
        if ($categoryId) {
            $where['category_id'] = $categoryId;
        }

        $list = \app\admin\model\tourism\News::where($where)
            ->order('weigh desc, id desc')
            ->paginate(10);
            
        $this->success('请求成功', $list);
    }
    
    /**
     * 资讯详情
     */
    public function detail()
    {
        $id = $this->request->get('id/d');
        if (!$id) {
            $this->error('参数错误');
        }
        $info = \app\admin\model\tourism\News::get($id);
        if (!$info || $info['status'] == 'hidden') {
            $this->error('未找到该资讯');
        }
        // 增加浏览量
        $info->setInc('views');
        
        $this->success('请求成功', $info);
    }
} 
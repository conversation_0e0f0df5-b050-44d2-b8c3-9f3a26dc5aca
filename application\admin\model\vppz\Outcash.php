<?php

namespace app\admin\model\vppz;

use think\Model;


class Outcash extends Model
{

    

    

    // 表名
    protected $name = 'vppz_outcash';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'biz_text',
        'status_text',
        'status_time_text',
        'channel_text'
    ];
    

    
    public function getBizList()
    {
        return ['staff' => __('Biz staff'), 'user' => __('Biz user'), 'seller' => __('Biz seller'), 'area' => __('Biz area')];
    }

    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }

    public function getChannelList()
    {
        return ['wx' => __('Channel wx'), 'wxpay' => __('Channel wxpay'), 'ali' => __('Channel ali'), 'bank' => __('Channel bank')];
    }


    public function getBizTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['biz']) ? $data['biz'] : '');
        $list = $this->getBizList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status_time']) ? $data['status_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getChannelTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['channel']) ? $data['channel'] : '');
        $list = $this->getChannelList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setStatusTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}

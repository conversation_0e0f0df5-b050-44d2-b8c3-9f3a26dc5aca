<?php
namespace app\api\controller\vppz;


use think\Cache;
use think\Db;

use \app\admin\model\vppz\User as UserModel;
use \app\admin\model\vppz\Order as OrderModel;

use \addons\vppz\library\Vpower;


class Seller extends App
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize(){
        parent::_initialize();

		$this->UserModel = new UserModel;
		$this->OrderModel = new OrderModel;
    }

	/**
     * 推广者自助注册
     */
	public function reg(){
		$area=$this->_area;
		$mine=$this->_mine;
		
		// 如果已经是推广者
		if($mine['seller_switch']==1){
			$this->success('已成为推广者');
		}
		
		if($area['seller_reg']!=3){
			$this->error('当前城市暂停开通推广者，如有疑问请联系客服');
		}

		$ret=$this->UserModel->save(['seller_switch'=>1],['id'=>$mine['id']]);
		if(!($ret>0)){
			$this->error("开通失败，请重试");
		}

		$this->success('已成为推广者');
	}

	/**
     * 我的账户记录
     */
	public function moneys(){
		$area=$this->_area;
		$mine=$this->_mine;
		
		$start=input('start');
		if(!isset($start) || empty($start) || intval($start<=0)){
			$start=0;
		}else{
			$start=intval($start);
		}
		$limit=20;
		
		$where=[
			'app_id'=>$this->app_id,
			'user_id'=>$mine['id'],
			'who'=>'seller'
		];
		
		$MoneyModel = new \app\admin\model\vppz\Money;
		$list = $MoneyModel->where($where)->limit($start,$limit)->order('id','desc')->select();

		$more=1;
		if(empty($list) || count($list)<$limit){
			$more=0;
		}
		$start+=count($list);

		$this->success('',array(
			'now'=>time(),
			'list'=>$list,
			'start'=>$start,
			'more'=>$more
		));
	}

	/**
     * 提现
     */
	public function outcash(){
		$cfg=$this->_cfg;
		$area=$this->_area;
		$mine=$this->_mine;
	
		$form = input('form');
		if(empty($form)){
			$this->error("内容填写有误，请重试");
		}
		$form = json_decode(urldecode($form),true);
		if(!$form){
			$this->error("内容填写有误，请重试");
		}

		$money = floatval($form['money']);
		$channel = $cfg['outcash_channel'];

		// 验证
		if ($money < intval($cfg['outcash_min'])){
			$this->error('单笔提现至少满'.$cfg['outcash_min'].'元');
		}
		if ($money > intval($cfg['outcash_max'])) {
			$this->error('单笔提现最多'.$cfg['outcash_max'].'元');
		}
		if ($money > $mine['sell_money']){
			$this->error('提现金额不能超过账户余额');
		}
		// 距离上传提现是否超过间隔
		$OutcashModel = new \app\admin\model\vppz\Outcash;
		$last_outcash = $OutcashModel->where(['app_id'=>$this->app_id,'biz'=>'seller','user_id'=>$mine['id']])->order('id','DESC')->find();
		if($last_outcash && (time()-$last_outcash['createtime']<$cfg['outcash_sp']*86400)){
			$this->error('提现间隔不能小于'.$cfg['outcash_sp'].'天');
		}
		
		$name='';
		$account='';
		$realname='';
		if($channel=='wx'){
			// 微信转账
			$account = $form['account'];
			$realname = $form['realname'];
			
			if(empty($account)){
				$this->error('请填写收款微信账号');
			}

			if(empty($realname)){
				$this->error('请填写收款账号真实姓名');
			}
			$name='微信转账';
		}else if($channel=='ali'){
			// 支付宝转账
			$account = $form['account'];
			$realname = $form['realname'];
			
			if(empty($account)){
				$this->error('请填写收款支付宝账号');
			}

			if(empty($realname)){
				$this->error('请填写收款账号真实姓名');
			}
			$name='支付宝转账';
		}else if($channel=='bank'){
			// 银行转账
			$name = $form['name'];
			$account = $form['account'];
			$realname = $form['realname'];
			
			if(empty($name)){
				$this->error('请填写开户银行');
			}

			if(empty($account)){
				$this->error('请填写银行账号');
			}

			if(empty($realname)){
				$this->error('请填写收款账号真实姓名');
			}
		}

		// 根据提现方式验证
		$mobile = $form['mobile'];
		if(empty($mobile)){
			$this->error('请填写您的手机号码');
		}

		// 先账户扣款，提现增款
		$ret = Db::name('vppz_user')->where(['app_id'=>$this->app_id,'id'=>$mine['id'],'sell_money'=>$mine['sell_money']])->dec('sell_money',$money)->inc('sell_outcash',$money)->update();
		if(!($ret>0)){
			$this->error("操作失败，请重试");
		}


		// 再生成提现申请记录
		$outcash=array(
			'app_id'=>$this->app_id,
			'area_id'=>$mine['area_id'],
			'biz'=>'seller',
			'biz_id'=>$mine['id'],
			'user_id'=>$mine['id'],
			'nickname'=>$mine['nickname'],
			'mobile'=>$mobile,
			'openid'=>$mine['openid'],
			'money'=>$money,
			'money_before'=>$mine['sell_money'],
			'money_after'=>$mine['sell_money']-$money,
			'cash'=>$money,	// 目前没有手续费
			'status'=>0,
			'channel'=>$channel,
			'channel_name'=>$name,
			'channel_account'=>$account,
			'channel_realname'=>$realname,
		);

		$ret=$OutcashModel->save($outcash);
		$outcash['id'] = $OutcashModel->id;
		if(empty($ret) || empty($outcash['id'])){
			$this->error('提现发起失败，请联系客服处理');
		}

		$this->recordMoney([
			'who'=>'seller',
			'who_id'=>$mine['id'],
			'who_name'=>$mine['nickname'],
			'user_id'=>$mine['id'],
			'money'=> 0-$money,
			'biz'=>'outcash',
			'biz_id'=>$outcash['id'],
			'biz_type'=>'outcash',
			'biz_name'=>'提现'.$money.'元',
			'remark'=>$name
		]);

		$this->success('请耐心等待审核，通过后48小时内将对您的账户打款，请留意账户通知');
	}

}
# 医院管理系统 - 医院类型功能

## 功能概述
在现有的医院管理系统中添加了医院类型管理功能，包括：
- 医院类型的增删改查
- 医院与医院类型的关联
- 医院类型的启用/禁用状态管理

## 新增文件

### 1. 数据库相关
- `hospital_tables.sql` - 数据库表结构和初始数据
- `menu_insert.sql` - 菜单配置SQL

### 2. 模型文件
- `application/admin/model/vppz/HospitalType.php` - 医院类型模型

### 3. 控制器文件
- `application/admin/controller/vppz/HospitalType.php` - 医院类型控制器

### 4. 视图文件
- `application/admin/view/vppz/hospitaltype/index.html` - 医院类型列表页
- `application/admin/view/vppz/hospitaltype/add.html` - 添加医院类型页
- `application/admin/view/vppz/hospitaltype/edit.html` - 编辑医院类型页
- `application/admin/view/vppz/hospitaltype/recyclebin.html` - 回收站页

### 5. 前端资源
- `public/assets/js/backend/vppz/hospitaltype.js` - 医院类型管理JS

## 修改的文件

### 1. 医院模型
- `application/admin/model/vppz/Hospital.php`
  - 添加了医院类型关联关系
  - 添加了类型文本获取器

### 2. 医院控制器
- `application/admin/controller/vppz/Hospital.php`
  - 添加了医院类型列表获取
  - 修改了查询以包含医院类型关联

### 3. 医院视图
- `application/admin/view/vppz/hospital/add.html`
  - 将医院类型字段改为下拉选择
  - 添加了医院分类选择器

- `application/admin/view/vppz/hospital/edit.html`
  - 将医院类型字段改为下拉选择
  - 添加了医院分类选择器

## 数据库变更

### 新增表
```sql
CREATE TABLE `fa_vppz_hospital_type` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '应用ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '类型名称',
  `description` varchar(255) DEFAULT '' COMMENT '类型描述',
  `use_switch` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医院类型表';
```

### 修改表
```sql
ALTER TABLE `fa_vppz_hospital` ADD COLUMN `type_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '医院类型ID' AFTER `label`;
```

## 默认数据
系统预置了以下医院类型：
1. 公立医院 - 由政府举办的医院
2. 民营医院 - 由社会资本举办的医院
3. 专科医院 - 专门治疗某种疾病的医院
4. 综合医院 - 设有多个科室的综合性医院
5. 中医医院 - 以中医药为主的医院

## 安装步骤

1. 执行数据库脚本：
   ```bash
   # 在MySQL中执行
   source hospital_tables.sql
   ```

2. 清理缓存：
   ```bash
   # 清理模板缓存和权限缓存
   rm -rf runtime/cache/*
   rm -rf runtime/temp/*
   ```

3. 刷新后台菜单权限

## 功能特点

1. **完整的CRUD操作** - 支持医院类型的增删改查
2. **软删除支持** - 删除的医院类型可以在回收站中恢复
3. **权重排序** - 支持通过权重字段进行排序
4. **启用状态管理** - 可以启用或禁用医院类型
5. **关联查询** - 医院列表中显示对应的医院类型名称
6. **下拉选择** - 在添加/编辑医院时通过下拉框选择医院类型

## 使用说明

1. **管理医院类型**：
   - 进入后台管理 → vppz → 医院类型管理
   - 可以添加、编辑、删除医院类型
   - 可以设置医院类型的启用状态和权重

2. **关联医院类型**：
   - 在添加或编辑医院时，选择对应的医院分类
   - 医院列表中会显示对应的医院类型

3. **权限管理**：
   - 管理员可以为不同角色分配医院类型管理权限
   - 支持细粒度的权限控制（查看、添加、编辑、删除等）

## 注意事项

1. 医院类型删除前请确保没有医院关联该类型
2. 禁用的医院类型在医院管理中不会显示在下拉列表中
3. 权重值越大，在列表中排序越靠前
4. 删除的医院类型可以在回收站中恢复

<?php

namespace app\index\controller\tourism;

use app\common\controller\Frontend;

class News extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = 'default';

    public function detail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error("文章未找到");
        }
        
        $news = \app\admin\model\tourism\News::get($id);
        if (!$news || $news->status == 'hidden') {
            $this->error("文章未找到或已隐藏");
        }

        // 查找所属栏目信息
        $category = \app\admin\model\tourism\Category::get($news->category_id);

        // 增加浏览量
        $news->setInc('views');

        $this->view->assign('news', $news);
        $this->view->assign('category', $category);
        $this->view->assign('title', $news->title);
        return $this->view->fetch();
    }
} 
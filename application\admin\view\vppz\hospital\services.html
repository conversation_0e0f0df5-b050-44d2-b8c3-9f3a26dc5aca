<form id="services-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	
	
	{volist name="services" id="vo" empty="<div class='alert alert-info-light' style='margin-bottom:10px;'><b>当前还没有服务内容</b><br>请先前往服务管理创建服务</div>"}
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{$vo.name}:</label>
			<div class="col-xs-12 col-sm-8">

				<div class="input-group">
					<div class="input-group-addon">
						<input type="checkbox"  name="services[{$vo.id}][use_switch]"  value="1" {if isset($vo._hospital) && $vo._hospital.use_switch==1}checked{/if}/>
					</div>

					<div class="input-group-addon">价格:</div>
					<input type="number" step="0.01" name="services[{$vo.id}][price]"  class="form-control" value="{$vo._hospital?$vo._hospital.price:''}" placeholder="平台默认价格：{$vo.price}元/次" />
				</div>
			</div>
		</div>
	{/volist}
    

	{if condition="$services && count($services)>0"}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
	{/if}
</form>

<?php

namespace app\admin\controller\vppz;

use app\common\controller\Backend;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Staff extends Base
{

    /**
     * Staff模型对象
     * @var \app\admin\model\vppz\Staff
     */
    protected $model = null;

	protected $searchFields = ['id','user_id','area.name','nickname','realname','mobile'];
	protected $multiFields = ['odmanar','master','stop_switch']; 

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\Staff;
        $this->view->assign("sexList", $this->model->getSexList());
        $this->view->assign("odmanarList", $this->model->getOdmanarList());
        $this->view->assign("masterList", $this->model->getMasterList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

			$adminAreasWhere=$this->adminAreasWhere('area.id');

            $list = $this->model
                    ->with(['user','area'])
                    ->where($where)->where($adminAreasWhere)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('user')->visible(['nickname','avatar']);
				$row->getRelation('area')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


	
    /**
     * 审核
     */
    public function verify()
    {	
		$ids = input('ids');; 
        $staff = $this->model->get(['id' => $ids]);
        if (!$staff) {
            $this->error(__('No Results were found'));
        }
        
		if ($this->request->isPost()) {
			$row = input('row/a');
			$row['status_time']=time();
			$this->model->save($row,['id' =>$staff['id']]);
			
			$this->success("保存成功");
		}else{
			
			$this->view->assign("row", $staff);
			return $this->view->fetch();
		}
    }

}

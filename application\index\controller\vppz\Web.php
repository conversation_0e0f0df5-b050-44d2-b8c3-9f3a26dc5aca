<?php
namespace app\index\controller\vppz;

use app\common\controller\Frontend;

use think\Config;
use think\Cache;
use think\Db;

use \app\admin\model\vppz\App as AppModel;
use \app\admin\model\vppz\User as UserModel;

use \addons\vppz\library\Vpower;

use EasyWeChat\Factory as EasyWeChatFactory;

class Web extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    public function _initialize(){
        parent::_initialize();
		
		$this->AppModel = new AppModel;
		$this->UserModel = new UserModel;
		
		// 获取当前应用信息即配置
		$this->app_id = input('appid');
		if(empty($this->app_id)){
			$this->error('缺少参数appid');
		}
		$this->_cfg = $this->AppModel->find($this->app_id);
		if(empty($this->_cfg)){
			$this->error('当前appid应用不存在');
		}
    }


    public function index(){
		$cfg=$this->_cfg;

		$config = [
			'app_id' => $cfg['mpappid'],
			'secret' => $cfg['mpappsecret'],
			'oauth' => [
			  'scopes'   => ['snsapi_base'],
			  'callback' => '/index/vppz/Web/bindacc?appid='.$this->app_id,
			],
			// ..
		];


		$app = EasyWeChatFactory::officialAccount($config);
		$oauth = $app->oauth;

		$oauth->redirect()->send();
	}
	
	public function bindacc(){
		$cfg=$this->_cfg;

		$config = [
			'app_id' => $cfg['mpappid'],
			'secret' => $cfg['mpappsecret'],
		];

		$app = EasyWeChatFactory::officialAccount($config);
		$oauth = $app->oauth;

		// 获取 OAuth 授权结果用户信息
		$user = $oauth->user();

		//var_dump($user);
		
		$this->assign('user', $user);

		return $this->view->fetch();
	}

}
<?php
namespace app\api\controller\vppz;


use think\Cache;
use think\Db;

use \app\admin\model\vppz\User as UserModel;
use \app\admin\model\vppz\Staff as StaffModel;
use \app\admin\model\vppz\Order as OrderModel;
use \app\admin\model\vppz\Client as ClientModel;

use \addons\vppz\library\Vpower;


class User extends App
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize(){
        parent::_initialize();

		$this->UserModel = new UserModel;
		$this->StaffModel = new StaffModel;
		$this->OrderModel = new OrderModel;
		$this->ClientModel = new ClientModel;
    }


    /**
     * 服务下单页
     */
    public function index(){
		//$area=$this->_area;// 用户控制器不区分运营区域
		$mine=$this->_mine;

		$statistic=array();
		// 待支付
		$statistic['topays'] = $this->OrderModel->where(['app_id'=>$this->app_id,'user_id'=>$mine['id'],'status'=>10])->count();
		// 待服务
		$statistic['todos'] = $this->OrderModel->where(['app_id'=>$this->app_id,'user_id'=>$mine['id'],'status'=>20])->count();
		
		// 获取我的服务员身份信息
		$staff = $this->StaffModel->where(['app_id'=>$this->app_id,'user_id'=>$mine['id']])->find();
			
		$this->success('',array(
			'now'=>time(),
			'mine'=>$mine,
			'statistic'=>$statistic,
			'staff'=>$staff
		));
    }
	
	// 用户个人资料修改
	public function profile(){
		//$area=$this->_area;// 用户控制器不区分运营区域
		$mine=$this->_mine;

		$submit = input('submit');

		if($submit=='save'){
			// 接受参数
			$avatar = input('avatar');
			$nickname = input('nickname');

			// 参数验证
			if(empty($avatar)){
				$this->error("请上传您的头像");
			}

			if(empty($nickname)){
				$this->error("请填写您的称呼");
			}
			if(mb_strlen($nickname)<2){
				$this->error("称呼至少2个字");
			}
			if(mb_strlen($nickname)>10){
				$this->error("称呼最多10个字");
			}
			
			/**
			if(empty($form['mobile'])){
				$this->error("请填写您的手机号");
			}
			**/

			// 修改
			$userUp=array(
				'nickname'=>$nickname,
				'avatar'=>$avatar,
				//'age'=>intval($form['age']),
				//'mobile'=>$form['mobile']
			);

			$ret = $this->UserModel->save($userUp,['id'=>$mine['id']]);
			if(!($ret>0)){
				$this->error("保存失败，请重试");
			}

			$this->success('保存成功');
		}
	}

	// 用户手机修改 TODO 目前并未做验证
	public function mobile(){
		//$area=$this->_area;// 用户控制器不区分运营区域
		$mine=$this->_mine;

		$submit = input('submit');

		if($submit=='save'){
			// 接受参数
			$mobile = input('mobile');

			// 参数验证
			if(empty($mobile)){
				$this->error("请填写您的手机号");
			}

			// 修改
			$userUp=array(
				'mobile'=>$mobile
			);

			$ret = $this->UserModel->save($userUp,['id'=>$mine['id']]);
			if(!($ret>0)){
				$this->error("保存失败，请重试");
			}

			$this->success('保存成功');
		}
	}
	
	// 更新前端用户信息
	public function update(){
		$mine=$this->_mine;
		$this->success('',['mine'=>$mine]);
	}
	

	// 服务对象管理
	public function clients(){
		$mine=$this->_mine;
		$clients = $this->ClientModel->where(['app_id'=>$this->app_id,'user_id'=>$mine['id']])->select();
		$this->success('',array('clients'=>$clients));
	}

	// 服务对象管理
	public function client(){
		$mine=$this->_mine;
		$submit = input('submit');
		if($submit=='save'){
			// 接受参数
			$form = input('form');
			if(empty($form)){
				$this->error("内容填写有误，请重试");
			}
			$form = json_decode(urldecode($form),true);
			if(!$form){
				$this->error("内容填写有误，请重试");
			}

			// 保存client
			// 去除client多余数据,添加client额外数据
			$client=array(
				'app_id'=>$this->app_id,
				'user_id'=>$mine['id'],
				'openid'=>$mine['openid'],
				'name'=>$form['name'],
				'sex'=>intval($form['sex']),
				'age'=>intval($form['age']),
				'mobile'=>$form['mobile'],
				//'idcard'=>$form['idcard']	保护隐私，暂不增加身份证号
			);

			$ret=$this->ClientModel->save($client);
			$client['id'] = $this->ClientModel->id;
			if(empty($ret) || empty($client['id'])){
				$this->error('服务器刚刚走神了，重试看看呢');
			}

			$this->success('新增成功');
		}else if('remove'==$submit){
			$id = input('id');
			if(!($id>0)){
				$this->error("请选择要移除的对象");
			}
			
			//ClientModel::destroy($id);
			Db::name('vppz_client')->where(['user_id'=>$mine['id'],'id'=>$id])->delete();

			$this->success('移除成功');
		}
	}

}
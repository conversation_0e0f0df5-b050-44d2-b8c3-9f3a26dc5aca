<?php
namespace app\api\controller\vppz;

use app\common\library\Upload;
use app\common\exception\UploadException;

use EasyWeChat\Factory as EasyWeChatFactory;

class Common extends AppBase
{
    //protected $noNeedLogin = ['*'];
    //protected $noNeedRight = ['*'];
	

    public function _initialize(){
        parent::_initialize();
		
    }
	

	// 上传 目前为普通上传，第三方上传未测试，可参考cms: api/common/upload
	public function upload(){
		
		// 为分辨前端上传的文件
		$fileName = input('path');

		$attachment = null;
		//默认普通上传文件
		$file = $this->request->file('file');
		try {
			$upload = new Upload($file);
			$attachment = $upload->upload();
		} catch (UploadException $e) {
			$this->error($e->getMessage());
		}

		$this->success(__('Uploaded successful'), [
			'file'=>$fileName,// 前端path
			'path' => $attachment->url,// 后端端path
			'url' => cdnurl($attachment->url, true)// 完整访问地址
		]);
	}


	// 通用接口：生成小程序码
	public function accode(){

		$page = input('page');
		$scene = input('scene');
		
		$WxappApi = EasyWeChatFactory::miniProgram([
			'app_id'    => $this->_cfg['wxapp_id'],
			'secret'    => $this->_cfg['wxapp_secret'],
			'log' => [
				'level' => 'debug',
				'file'  => '/tmp/easywechat.log',
			],
			// ...
		]);
		
		$response = $WxappApi->app_code->getUnlimit($scene, [
			'page'  => $page,
			'width' => 100,
			'auto_color'=>true
		]);
		
		/***
		$response = $WxappApi->app_code->get('vp_pz/pages/index/index', [
			'width' => 100,
			'line_color' => [
				'r' => 11,
				'g' => 181,
				'b' => 132,
			],
		]);
		**/

		header('Content-type: image/jpg');
		echo $response;

		/**
		$resp = vp_getCodeUnlimit($scene,$page);
		//echo $page;
		//echo "  ";
		//var_dump ($scene);
		//var_dump( $resp);
		header('Content-type: image/jpg');
		echo $resp['content'];
		**/
	}

}
-- 医院类型表
CREATE TABLE `fa_vppz_hospital_type` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '应用ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '类型名称',
  `description` varchar(255) DEFAULT '' COMMENT '类型描述',
  `use_switch` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医院类型表';

-- 为现有医院表添加类型字段
ALTER TABLE `fa_vppz_hospital` ADD COLUMN `type_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '医院类型ID' AFTER `label`;

-- 插入默认医院类型数据（需要根据实际的app_id调整）
INSERT INTO `fa_vppz_hospital_type` (`app_id`, `name`, `description`, `use_switch`, `weigh`, `createtime`, `updatetime`) VALUES
(1, '公立医院', '由政府举办的医院', 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '民营医院', '由社会资本举办的医院', 1, 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '专科医院', '专门治疗某种疾病的医院', 1, 80, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '综合医院', '设有多个科室的综合性医院', 1, 70, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(1, '中医医院', '以中医药为主的医院', 1, 60, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入医院类型管理菜单
-- 首先查找vppz模块的父菜单ID
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`)
SELECT 'file', id, 'vppz/hospitaltype', '医院类型管理', 'fa fa-hospital-o', '', '医院类型管理', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
FROM `fa_auth_rule` WHERE `name` = 'vppz' LIMIT 1;

-- 获取刚插入的医院类型管理菜单ID并插入子菜单
SET @hospital_type_id = LAST_INSERT_ID();

INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @hospital_type_id, 'vppz/hospitaltype/index', '查看', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/add', '添加', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/edit', '编辑', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/del', '删除', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/multi', '批量更新', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/recyclebin', '回收站', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/restore', '还原', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'),
('file', @hospital_type_id, 'vppz/hospitaltype/destroy', '真实删除', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

/*
 * Component: alert
 * ----------------
 */

.alert {
  .border-radius(3px);
  h4 {
    font-weight: 600;
  }
  .icon {
    margin-right: 10px;
  }
  .close {
    color: #000;
    .opacity(.2);
    &:hover {
      .opacity(.5);
    }
  }
  a {
    color: #fff;
    text-decoration: underline;
  }
}

//Alert Variants
.alert-success {
  &:extend(.bg-green);
  border-color: darken(@green, 5%);
}

.alert-danger,
.alert-error {
  &:extend(.bg-red);
  border-color: darken(@red, 5%);
}

.alert-warning {
  &:extend(.bg-yellow);
  border-color: darken(@yellow, 5%);
}

.alert-info {
  &:extend(.bg-aqua);
  border-color: darken(@aqua, 5%);
}

.alert-primary-light {
  background-color: @primary-light-bg;
  border-color: @primary-light-border;
  color: @primary-light-text;
  a {
    color: darken(@primary-light-text, 5%);
  }
}

.alert-success-light {
  background-color: @success-light-bg;
  border-color: @success-light-border;
  color: @success-light-text;
  a {
    color: darken(@success-light-text, 5%);
  }
}

.alert-danger-light,
.alert-error-light {
  background-color: @danger-light-bg;
  border-color: @danger-light-border;
  color: @danger-light-text;
  a {
    color: darken(@danger-light-text, 5%);
  }
}

.alert-warning-light {
  background-color: @warning-light-bg;
  border-color: @warning-light-border;
  color: @warning-light-text;
  a {
    color: darken(@warning-light-text, 5%);
  }
}

.alert-info-light {
  background-color: @info-light-bg;
  border-color: @info-light-border;
  color: @info-light-text;
  a {
    color: darken(@info-light-text, 5%);
  }
}
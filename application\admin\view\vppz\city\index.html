<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs nav-custom-condition">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            <li class=""><a href="#t-1" data-value='1' data-toggle="tab">自定义搜索条件1</a></li>
            <li class=""><a href="#t-2" data-value='2' data-toggle="tab">自定义搜索条件2</a></li>
        </ul>
    </div>


    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,delete')}
                        <a class="btn btn-info btn-disabled disabled btn-selected" href="javascript:;"><i class="fa fa-leaf"></i> 获取选中项</a>
                        <div class="dropdown btn-group">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> <?= __('More') ?></a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                        <a class="btn btn-success btn-singlesearch" href="javascript:;"><i class="fa fa-user"></i> 自定义搜索</a>
                        <a class="btn btn-success btn-change btn-start" data-params="action=start" data-url="example/bootstraptable/start" href="javascript:;"><i class="fa fa-play"></i> 启动</a>
                        <a class="btn btn-danger btn-change btn-pause" data-params="action=pause" data-url="example/bootstraptable/pause" href="javascript:;"><i class="fa fa-pause"></i> 暂停</a>
                        <a href="javascript:;" class="btn btn-default" style="font-size:14px;color:dodgerblue;">
                            <i class="fa fa-dollar"></i>
                            <span class="extend">
                                金额：<span id="money">0</span>
                                单价：<span id="price">0</span>
                            </span>
                        </a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">

                    </table>

                </div>
            </div>

        </div>
    </div>
</div>
<!--
<script id="categorytpl" type="text/html">
    <div class="row">
        <div class="col-xs-12">
            <div class="form-inline" data-toggle="cxselect" data-selects="group,admin">
                <select class="group form-control" name="group" data-url="example/bootstraptable/cxselect?type=group"></select>
                <select class="admin form-control" name="admin_id" data-url="example/bootstraptable/cxselect?type=admin" data-query-name="group_id"></select>
                <input type="hidden" class="operate" data-name="admin_id" value="=" />
            </div>
        </div>
    </div>
</script>
-->



<script>
require.callback = function () {
	define('backend/vppz/city',['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
		var Controller = {
			index: function () {
				// 初始化表格参数配置
				Table.api.init({
					extend: {
						index_url: 'vppz/city/index',
						add_url: 'vppz/city/add',
						edit_url: 'vppz/city/edit',
						del_url: 'vppz/city/del',
						multi_url: 'test/multi',
						table: 'city',
					}
				});

				var table = $("#table");

				//当表格数据加载完成时
				table.on('load-success.bs.table', function (e, data) {
					//这里可以获取从服务端获取的JSON数据
					console.log(data);
					//这里我们手动设置底部的值
					$("#money").text(data.extend.money);
					$("#price").text(data.extend.price);
				});
				
				var formatter_percent=function(value,row,index){
					return value+'%';
				};
				var formatter_tax_city=function(value,row,index){
					return (100-row.profit-row.tax_master-row.tax_seller-row.tax_plat)+'%';
				};

				// 初始化表格
				table.bootstrapTable({
					url: $.fn.bootstrapTable.defaults.extend.index_url,
					pk: 'id',
					sortName: 'id',
					columns: [
						[
							{checkbox: true},
							{field: 'id', title:'ID'},
							{field: 'name', title:'城市'},
							{field: 'odmode', title:'接单模式',searchList: {'0':'抢单','10':'派单'}, formatter: Table.api.formatter.normal},
							{field: 'profit', title:'服务者收益率',formatter:formatter_percent},
							{field: 'tax_master', title:'团队长收益率',formatter:formatter_percent},
							{field: 'tax_plat', title:'平台收益率',formatter:formatter_percent},
							{field: 'tax_city',title:'城市收益率',formatter:formatter_tax_city},
							//{field: 'flag', title: __('Flag'), searchList: {"hot": __('Flag hot'), "index": __('Flag index'), "recommend": __('Flag recommend')}, operate: 'FIND_IN_SET', formatter: Table.api.formatter.label},
							{field: 'create_time', title:'创建时间', operate: 'RANGE', addclass: 'datetimerange', formatter: Table.api.formatter.datetime},
							{field: 'update_time', title:'更新时间', operate: 'RANGE', addclass: 'datetimerange', formatter: Table.api.formatter.datetime, visible: false},
							//{field: 'weigh', title: __('Weigh'), operate: false, visible: false},
							//{field: 'switch', title: __('Switch'), searchList: {"1": __('Yes'), "0": __('No')}, formatter: Table.api.formatter.toggle},
							{field: 'status', title: '是否启用', searchList:{'0':'停用','1':'启用'}, formatter: Table.api.formatter.toggle},
							{
								field: 'buttons',
								width: "120px",
								title: __('按钮组'),
								table: table,
								events: Table.api.events.operate,
								buttons: [
									{
										name: 'detail',
										text: __('弹出窗口打开'),
										title: __('弹出窗口打开'),
										classname: 'btn btn-xs btn-primary btn-dialog',
										icon: 'fa fa-list',
										url: 'example/bootstraptable/detail',
										callback: function (data) {
											Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
										},
										visible: function (row) {
											//返回true时按钮显示,返回false隐藏
											return true;
										}
									},
									{
										name: 'ajax',
										text: __('发送Ajax'),
										title: __('发送Ajax'),
										classname: 'btn btn-xs btn-success btn-magic btn-ajax',
										icon: 'fa fa-magic',
										url: 'example/bootstraptable/detail',
										confirm: '确认发送',
										success: function (data, ret) {
											Layer.alert(ret.msg + ",返回数据：" + JSON.stringify(data));
											//如果需要阻止成功提示，则必须使用return false;
											//return false;
										},
										error: function (data, ret) {
											console.log(data, ret);
											Layer.alert(ret.msg);
											return false;
										}
									},
									{
										name: 'addtabs',
										text: __('新选项卡中打开'),
										title: __('新选项卡中打开'),
										classname: 'btn btn-xs btn-warning btn-addtabs',
										icon: 'fa fa-folder-o',
										url: 'example/bootstraptable/detail'
									}
								],
								formatter: Table.api.formatter.buttons
							},
							{
								field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
								buttons: [
									{
										name: 'detail',
										text: __('详情'),
										title: __('详情'),
										classname: 'btn btn-xs btn-primary btn-dialog',
										icon: 'fa fa-list',
										url: 'test/detail',
										callback: function (data) {
											Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
										}
									}],
								formatter: Table.api.formatter.operate
							}
						]
					]
				});

				// 绑定TAB事件
				$('.panel-heading a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
					var field = $(this).closest("ul").data("field");
					var value = $(this).data("value");
					var options = table.bootstrapTable('getOptions');
					options.pageNumber = 1;
					options.queryParams = function (params) {
						var filter = {};
						if (value !== '') {
							filter[field] = value;
						}
						params.filter = JSON.stringify(filter);
						return params;
					};
					table.bootstrapTable('refresh', {});
					return false;
				});

				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			add: function () {
				Controller.api.bindevent();
			},
			edit: function () {
				Controller.api.bindevent();
			},
			api: {
				bindevent: function () {
					Form.api.bindevent($("form[role=form]"));
				}
			}
		};
		return Controller;
	});
}
</script>
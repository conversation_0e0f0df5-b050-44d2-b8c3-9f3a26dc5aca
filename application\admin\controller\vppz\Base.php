<?php

namespace app\admin\controller\vppz;

use think\Db;
use app\common\controller\Backend;

use \app\admin\model\vppz\AreaAdmin as AreaAdminModel;
use \app\admin\model\vppz\Money as MoneyModel;

/**
 * 系统配置
 *
 * @icon fa fa-gears
 */
class Base extends Backend
{
    protected $model = null;
    //protected $noNeedRight = ['*'];
    protected $app_id = 1;

    public function _initialize()
    {
        parent::_initialize();
		
		/*** 插件发布版去除
		// vptest开头固定为演示账号，只能允许查看相关操作
		if(strpos($this->auth->username,'vptest')===0){
			if($this->request->action()!='index' && $this->request->isAjax()){
				 $this->error('当前演示账号无法进行修改和删除数据');
			}
		}
		**/

    }

	// 当前管理员可管理的区域，id数组
	protected function adminAreas(){
		$AreaAdminModel = new AreaAdminModel;
		$areas = $AreaAdminModel->where(['app_id'=>$this->app_id,'admin_id'=>$this->auth->id])->column('area_id');
		return $areas; 
	}
	
	// 根据当前管理员可管理的区域，id数组，组合where条件
	protected function adminAreasWhere($area_key){
		$aids = $this->adminAreas();
		if($this->auth->isSuperAdmin()){
			return [];
		}
		return [$area_key=>['IN',$aids]];
	}


	// 记录金额变化
	/**
	'who'=>'city',
	'who_id'=>$city['id'],
	'uid'=>$city['uid'],		// 城市负责人uid，可能为空，只用作记录，收入归城市，不归人
	'money'=> $tax_city_fee,
	'biz'=>'order',
	'biz_id'=>$order['id'],
	'biz_type'=>'city',
	'biz_name'=>$order['service_name'],
	'remark'=>'订单总价：'.$order['amount'].'元'
	***/
	protected function recordMoney($money){
		//if(empty($this->MoneyModel)){
		//	$this->MoneyModel = new MoneyModel;
		//}

		//$money['app_id']=$this->app_id;

		//$this->MoneyModel->save($money);

		$money['app_id']=$this->app_id;
		$money['createtime']=time();

		Db::name('vppz_money')->insert($money);
	}
}
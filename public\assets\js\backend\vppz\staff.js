define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/staff/index' + location.search,
                    add_url: 'vppz/staff/add',
                    edit_url: 'vppz/staff/edit',
                    //del_url: 'vppz/staff/del',
                    multi_url: 'vppz/staff/multi',
                    import_url: 'vppz/staff/import',
                    table: 'vppz_staff',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
						{field: 'user_id', title:'UID'},
                        {field: 'area.id', title:'所属运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false},
						{field: 'area.name', title:'所属运营区', operate: 'LIKE',searchable:false},
                        //{field: 'user_id', title: __('User_id')},
                        //{field: 'openid', title: __('Openid'), operate: 'LIKE'},
						{field: 'avatar', title: '头像', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
                        {field: 'nickname', title: '称呼', operate: 'LIKE'},
						{field: 'realname', title: '真实姓名', operate: 'LIKE'},
                        {field: 'sex', title: __('Sex'), searchList: {"1":__('Sex 1'),"2":__('Sex 2')}, formatter: Table.api.formatter.normal},
                        {field: 'age', title: '年龄',operate:'BETWEEN'},
                        {field: 'mobile', title: '手机号', operate: 'LIKE'},
                        {field: 'idcardnum', title: '身份证号', operate: 'LIKE',visible: false},
                        {field: 'odmanar', title: '派单员', table: table, formatter: Table.api.formatter.toggle,searchList: {"0":'否',"1":'是'}},
                        {field: 'master', title: '团长奖励',table: table, formatter: Table.api.formatter.toggle,searchList: {"0":'否',"1":'是'}},
                        {field: 'subs', title: '团员数',operate:'BETWEEN'},
						//{field: 'master_uid', title: '团长UID'},
                        {field: 'money', title: __('Money'), operate:'BETWEEN'},
                        {field: 'income', title: __('Income'), operate:'BETWEEN'},
                        {field: 'income_service', title: '服务总收入', operate:'BETWEEN'},
                        {field: 'income_master', title: '团队总提成', operate:'BETWEEN'},
                        {field: 'outcash', title: __('Outcash'), operate:'BETWEEN'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
                        {field: 'status', title: __('Status'), searchList: {"5":__('Status 5'),"10":__('Status 10'),"20":__('Status 20')}, formatter: Table.api.formatter.status,custom:{'5':'danger','10':'warning','20':'success'}},//"0":__('Status 0'),
                        {field: 'status_time', title: '审核时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
                        {field: 'stop_switch', title: '停用', table: table, formatter: Table.api.formatter.toggle,color:'danger',searchList: {"0":'正常',"1":'停用'}},
                        //{field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE'},
                        //{field: 'user.avatar', title: __('User.avatar'), operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
							buttons: [{
                                    name: 'verify',
                                    title: '审核',
									text: '审核',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-gavel',
                                    url: 'vppz/staff/verify',
                                    //callback: function (data) {
                                    //    Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    //}
                                }]	
						}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
		verify: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

<?php
/**
 * 医院类型功能安装脚本
 * 直接通过Web访问执行: http://your-domain/install_hospital_type.php
 */

// 数据库配置 - 请根据实际情况修改
$host = '127.0.0.1';
$username = 'root';
$password = '';
$database = 'fastadmin';
$port = 3306;

try {
    // 连接数据库
    $pdo = new PDO("mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>医院类型功能安装脚本</h2>";
    echo "<pre>";
    
    // SQL语句数组
    $sqlStatements = [
        // 创建医院类型表
        "CREATE TABLE IF NOT EXISTS `fa_vppz_hospital_type` (
          `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
          `app_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '应用ID',
          `name` varchar(50) NOT NULL DEFAULT '' COMMENT '类型名称',
          `description` varchar(255) DEFAULT '' COMMENT '类型描述',
          `use_switch` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
          `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
          `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
          `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
          `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
          PRIMARY KEY (`id`),
          KEY `app_id` (`app_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医院类型表'",
        
        // 检查并添加type_id字段到医院表
        "ALTER TABLE `fa_vppz_hospital` ADD COLUMN IF NOT EXISTS `type_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '医院类型ID' AFTER `label`",
        
        // 插入默认医院类型数据
        "INSERT IGNORE INTO `fa_vppz_hospital_type` (`app_id`, `name`, `description`, `use_switch`, `weigh`, `createtime`, `updatetime`) VALUES
        (1, '公立医院', '由政府举办的医院', 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (1, '民营医院', '由社会资本举办的医院', 1, 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (1, '专科医院', '专门治疗某种疾病的医院', 1, 80, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (1, '综合医院', '设有多个科室的综合性医院', 1, 70, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
        (1, '中医医院', '以中医药为主的医院', 1, 60, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())"
    ];
    
    // 菜单SQL语句
    $menuSqlStatements = [
        // 插入主菜单
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', id, 'vppz/hospitaltype', '医院类型管理', 'fa fa-hospital-o', '', '医院类型管理', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        FROM `fa_auth_rule` WHERE `name` = 'vppz' LIMIT 1",
        
        // 插入子菜单
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/index', '查看', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/index')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/add', '添加', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/add')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/edit', '编辑', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/edit')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/del', '删除', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/del')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/multi', '批量更新', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/multi')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/recyclebin', '回收站', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/recyclebin')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/restore', '还原', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/restore')",
        
        "INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
        SELECT 'file', 
               (SELECT id FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype' LIMIT 1),
               'vppz/hospitaltype/destroy', '真实删除', 'fa fa-circle-o', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
        WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_rule` WHERE `name` = 'vppz/hospitaltype/destroy')"
    ];
    
    $successCount = 0;
    $errorCount = 0;
    
    // 执行基础SQL语句
    echo "执行基础表结构和数据...\n";
    foreach ($sqlStatements as $index => $sql) {
        try {
            $pdo->exec($sql);
            $successCount++;
            echo "✓ SQL " . ($index + 1) . " 执行成功\n";
        } catch (PDOException $e) {
            $errorCount++;
            echo "✗ SQL " . ($index + 1) . " 执行失败: " . $e->getMessage() . "\n";
        }
    }
    
    // 执行菜单SQL语句
    echo "\n执行菜单权限配置...\n";
    foreach ($menuSqlStatements as $index => $sql) {
        try {
            $pdo->exec($sql);
            $successCount++;
            echo "✓ 菜单 " . ($index + 1) . " 执行成功\n";
        } catch (PDOException $e) {
            $errorCount++;
            echo "✗ 菜单 " . ($index + 1) . " 执行失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n执行完成！\n";
    echo "成功: {$successCount} 条\n";
    echo "失败: {$errorCount} 条\n";
    
    // 验证安装结果
    echo "\n验证安装结果...\n";
    
    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'fa_vppz_hospital_type'");
    if ($stmt->rowCount() > 0) {
        echo "✓ 医院类型表创建成功\n";
    } else {
        echo "✗ 医院类型表创建失败\n";
    }
    
    // 检查字段是否添加
    $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_hospital LIKE 'type_id'");
    if ($stmt->rowCount() > 0) {
        echo "✓ 医院表type_id字段添加成功\n";
    } else {
        echo "✗ 医院表type_id字段添加失败\n";
    }
    
    // 检查数据
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fa_vppz_hospital_type");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ 医院类型数据: " . $result['count'] . " 条\n";
    
    // 检查菜单
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fa_auth_rule WHERE name LIKE 'vppz/hospitaltype%'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ 菜单权限: " . $result['count'] . " 条\n";
    
    echo "\n安装完成！请刷新后台页面查看新功能。\n";
    echo "</pre>";
    
} catch (PDOException $e) {
    echo "<pre>";
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查数据库配置信息。\n";
    echo "</pre>";
}
?>

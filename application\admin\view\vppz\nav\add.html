<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所属运营区:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="vppz/area/index" class="form-control selectpage" name="row[area_id]" type="text" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">导航位置:</label>
        <div class="col-xs-12 col-sm-8"> 
            <select  id="c-cat" data-rule="required" class="form-control selectpicker" name="row[cat]">
                {foreach name="catList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
			<h6 class="text-muted">导航在二格位置时，标题和标题颜色设置无效</h6>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">导航标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">导航图片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-pic_image" data-rule="required" class="form-control" size="50" name="row[pic_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-pic_image" class="btn btn-danger faupload" data-input-id="c-pic_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-pic_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-pic_image" class="btn btn-primary fachoose" data-input-id="c-pic_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-pic_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-pic_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">标题颜色:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tcolor"  class="form-control" name="row[tcolor]" type="text">
			<h6 class="text-muted">填写颜色值，例：#55555。不填则为默认黑色</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">点击跳转:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-stype" data-rule="required" class="form-control selectpicker" name="row[stype]">
                {foreach name="stypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group" data-favisible="stype=1,3,4">
        <label class="control-label col-xs-12 col-sm-2">跳转页面/网页:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-stype_link" data-rule="required" class="form-control" name="row[stype_link]" type="text">
        </div>
    </div>
    <div class="form-group" data-favisible="stype=2">
        <label class="control-label col-xs-12 col-sm-2">自定义内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-stype_content" data-rule="required" class="form-control editor" rows="5" name="row[stype_content]" cols="50"></textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">是否显示:</label>
        <div class="col-xs-12 col-sm-8">
            <input  id="c-use_switch" name="row[use_switch]" type="hidden" value="1">
			<a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-use_switch"  data-yes="1" data-no="0">
			<i class="fa fa-toggle-on text-success  fa-2x"></i>
			</a>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

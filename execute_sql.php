<?php
/**
 * 执行医院类型功能SQL脚本
 */

// 引入ThinkPHP框架
require_once 'thinkphp/start.php';

use think\Db;
use think\Exception;

try {
    echo "开始执行医院类型功能SQL脚本...\n";
    
    // 读取SQL文件内容
    $sqlContent = file_get_contents('hospital_tables.sql');
    
    if (!$sqlContent) {
        throw new Exception("无法读取SQL文件");
    }
    
    // 分割SQL语句（按分号分割，但要处理注释）
    $sqlStatements = [];
    $lines = explode("\n", $sqlContent);
    $currentStatement = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        // 跳过空行和注释行
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        
        $currentStatement .= $line . ' ';
        
        // 如果行以分号结尾，表示一个完整的SQL语句
        if (substr($line, -1) === ';') {
            $sqlStatements[] = trim($currentStatement);
            $currentStatement = '';
        }
    }
    
    // 执行SQL语句
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($sqlStatements as $index => $sql) {
        if (empty($sql)) continue;
        
        try {
            echo "执行SQL " . ($index + 1) . ": " . substr($sql, 0, 50) . "...\n";
            
            // 特殊处理SET语句
            if (strpos($sql, 'SET @') === 0) {
                Db::execute($sql);
            } else {
                Db::execute($sql);
            }
            
            $successCount++;
            echo "✓ 执行成功\n";
            
        } catch (Exception $e) {
            $errorCount++;
            echo "✗ 执行失败: " . $e->getMessage() . "\n";
            
            // 如果是表已存在或字段已存在的错误，继续执行
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate column') !== false) {
                echo "  (忽略重复创建错误，继续执行)\n";
                continue;
            }
        }
    }
    
    echo "\n执行完成！\n";
    echo "成功: {$successCount} 条\n";
    echo "失败: {$errorCount} 条\n";
    
    // 验证表是否创建成功
    echo "\n验证表结构...\n";
    
    try {
        $tables = Db::query("SHOW TABLES LIKE 'fa_vppz_hospital_type'");
        if ($tables) {
            echo "✓ 医院类型表创建成功\n";
        } else {
            echo "✗ 医院类型表创建失败\n";
        }
        
        $columns = Db::query("SHOW COLUMNS FROM fa_vppz_hospital LIKE 'type_id'");
        if ($columns) {
            echo "✓ 医院表type_id字段添加成功\n";
        } else {
            echo "✗ 医院表type_id字段添加失败\n";
        }
        
        $typeCount = Db::name('vppz_hospital_type')->count();
        echo "✓ 医院类型数据: {$typeCount} 条\n";
        
        $menuCount = Db::name('auth_rule')->where('name', 'like', 'vppz/hospitaltype%')->count();
        echo "✓ 菜单权限: {$menuCount} 条\n";
        
    } catch (Exception $e) {
        echo "验证过程中出现错误: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "脚本执行失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n脚本执行完成！\n";
?>

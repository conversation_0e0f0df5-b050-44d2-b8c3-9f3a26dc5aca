<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

/**
 * 医院类型模型
 */
class HospitalType extends Model
{
    use SoftDelete;
    
    // 表名
    protected $name = 'vppz_hospital_type';
    
    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';
    
    // 追加属性
    protected $append = [
        'use_switch_text'
    ];

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    /**
     * 获取启用状态列表
     */
    public function getUseSwitchList()
    {
        return ['0' => __('Disabled'), '1' => __('Enabled')];
    }

    /**
     * 获取启用状态文本
     */
    public function getUseSwitchTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['use_switch']) ? $data['use_switch'] : '');
        $list = $this->getUseSwitchList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 关联医院
     */
    public function hospitals()
    {
        return $this->hasMany('Hospital', 'type_id');
    }
}

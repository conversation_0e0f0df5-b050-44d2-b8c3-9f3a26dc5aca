<?php

namespace app\admin\model\vppz;

use think\Model;


class Staff extends Model
{

    

    

    // 表名
    protected $name = 'vppz_staff';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'sex_text',
        'odmanar_text',
        'master_text',
        'status_text',
        'status_time_text',
		'avatar_url'	// 需在此处追加属性，才会触发获取器 getAvatarUrlAttr
    ];
    

    
    public function getSexList()
    {
        return ['1' => __('Sex 1'), '2' => __('Sex 2')];
    }

    public function getOdmanarList()
    {
        return ['0' => __('Odmanar 0'), '1' => __('Odmanar 1')];
    }

    public function getMasterList()
    {
        return ['0' => __('Master 0'), '1' => __('Master 1')];
    }

    public function getStatusList()
    {
        return ['5' => __('Status 5'), '10' => __('Status 10'), '20' => __('Status 20')];
    }


    public function getSexTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['sex']) ? $data['sex'] : '');
        $list = $this->getSexList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getOdmanarTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['odmanar']) ? $data['odmanar'] : '');
        $list = $this->getOdmanarList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getMasterTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['master']) ? $data['master'] : '');
        $list = $this->getMasterList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status_time']) ? $data['status_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setStatusTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

	public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

	// 获取器直接转换url
	public function getAvatarUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['avatar']);
    }




}

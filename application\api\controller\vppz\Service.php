<?php
namespace app\api\controller\vppz;


use think\Cache;


use \app\admin\model\vppz\Hospital as HospitalModel;
use \app\admin\model\vppz\Service as ServiceModel;
use \app\admin\model\vppz\HospitalService as HospitalServiceModel;
use \app\admin\model\vppz\Staff as StaffModel;

use \addons\vppz\library\Vpower;

class Service extends App
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize(){
        parent::_initialize();
		

		$this->HospitalModel = new HospitalModel;
		$this->ServiceModel = new ServiceModel;
		$this->HospitalServiceModel = new HospitalServiceModel;

		$this->StaffModel = new StaffModel;
    }


    /**
     * 服务下单页
     */
    public function order(){
		$area=$this->_area;
		$mine = $this->_mine;


		$svid = input('svid');
		$hid = input('hid');

		// 获取服务
		//$service = pdo_fetch('SELECT * FROM ' .tablename('vp_pz_service') . ' where  uniacid = :uniacid AND id=:id AND status=1 AND is_del=0 ', array(':uniacid' => $_W['uniacid'],':id'=>$svid));
		$service = $this->ServiceModel->where(['app_id'=>$this->app_id])->find($svid);
		if(empty($service)){
			$this->error('该服务不存在或暂停提供');
		}
		//$service['icon']=VP_IMAGE_URL($service['icon']);

		
		//$hospitals = pdo_fetchall('SELECT id,name,service FROM ' .tablename('vp_pz_hospital') . ' where  uniacid = :uniacid AND city_id=:city_id AND status=1 AND is_del=0 order by sort desc', array(':uniacid' => $_W['uniacid'],':city_id'=>$city['id']));
		
		// 获取当前运营区域支持该服务的医院，供前端选择
		$hospitals = $this->HospitalModel->field('id,name,avatar')->where(['app_id'=>$this->app_id,'area_id'=>$area['id'],'use_switch'=>1])->select();

		$hs=array();
		for($i=0;$i<count($hospitals);$i++){
			$_hs = $this->HospitalServiceModel->field('service_id,price,use_switch')->where(['hospital_id'=>$hospitals[$i]['id'],'service_id'=>$service['id'],'use_switch'=>1])->find();
			if($_hs){
				$hs[]=array(
					'id'=>$hospitals[$i]['id'],
					'name'=>$hospitals[$i]['name'],
					'service_id'=>$_hs['service_id'],
					'service_price'=>$_hs['price']
				);
			}
			
			/**
			$hospitals[$i]['service']=iunserializer($hospitals[$i]['service']);
			if($hospitals[$i]['service'][$service['id']] && $hospitals[$i]['service'][$service['id']]['status']==1){
				//$hospitals[$i]['service']=$hospitals[$i]['service'][$service['id']]['price'];
				$hs[]=array(
					'id'=>$hospitals[$i]['id'],
					'name'=>$hospitals[$i]['name'],
					'service_id'=>$service['id'],
					'service_price'=>$hospitals[$i]['service'][$service['id']]['price']
				);
			}
			**/
		}
		$hospitals=$hs;

		// 是否有专属服务员
		$my_staff=null;
		if($area['staff_card']==1 && $mine['my_staff_id']>0){
			$my_staff=$this->StaffModel->field('id,nickname,avatar,sex,age,mobile')->where(['app_id'=>$this->app_id,'id'=>$mine['my_staff_id'],'status'=>20,'stop_switch'=>0])->find();
		}
		
		$this->success('',array(
			'now'=>time(),
			'service'=>$service,
			'hospitals'=>$hospitals,
			'my_staff'=>$my_staff
		));
    }


}
define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/hospital/index' + location.search,
                    add_url: 'vppz/hospital/add',
                    edit_url: 'vppz/hospital/edit',
                    del_url: 'vppz/hospital/del',
                    multi_url: 'vppz/hospital/multi',
                    import_url: 'vppz/hospital/import',
                    table: 'vppz_hospital',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'area.id', title:'所属运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false},
						{field: 'area.name', title:'所属运营区', operate: 'LIKE',searchable:false},
                        {field: 'name', title: '医院名称', operate: 'LIKE'},
                        {field: 'avatar', title: '形象照', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
                        {field: 'rank', title:'医院等级', operate: 'LIKE'},
                        {field: 'label', title:'医院类型', operate: 'LIKE'},
                        //{field: 'province', title: __('Province'), operate: 'LIKE'},
                        {field: 'city', title: '所在地点', operate: 'LIKE'},
                        //{field: 'district', title: __('District'), operate: 'LIKE'},
                        {field: 'address', title: '详细地址', operate: 'LIKE',searchable:false},
                        //{field: 'lat', title: __('Lat'), operate:'BETWEEN'},
                        //{field: 'lng', title: __('Lng'), operate:'BETWEEN'},
                        //{field: 'weigh', title: __('Weigh'), operate: false},
                        {field: 'use_switch', title: '是否启用', table: table, formatter: Table.api.formatter.toggle,searchable:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
                        //{field: 'admin_id', title: __('Admin_id')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
							
							buttons: [{
                                    name: 'services',
                                    title: '服务设置',
									text: '服务设置',
                                    classname: 'btn btn-xs btn-success btn-dialog',
                                    icon: 'fa fa-list',
                                    url: 'vppz/hospital/services',
                                    //callback: function (data) {
                                    //    Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    //}
                                }]
						}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'vppz/hospital/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), align: 'left'},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '130px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'vppz/hospital/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'vppz/hospital/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
		services: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

/*
* 旅游资讯模块专属CSS样式
*/

/* 头部轮播图 */
.tourism-banner {
    margin-bottom: 30px;
}

.tourism-banner .carousel {
    height: 500px;
}

.tourism-banner .carousel-inner {
    height: 100%;
}

.tourism-banner .carousel-inner .item {
    height: 100%;
    background-size: cover;
    background-position: center;
}

.tourism-banner .carousel-control {
    background: none;
    opacity: 0.8;
}

.tourism-banner .carousel-control span {
    font-size: 36px;
    position: absolute;
    top: 50%;
    margin-top: -18px;
}

/* 栏目导航 */
.category-nav {
    margin-bottom: 40px;
    padding: 20px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.05);
}

.category-icon {
    text-align: center;
    padding: 15px;
    transition: all 0.3s ease;
}

.category-icon:hover {
    transform: translateY(-5px);
}

.category-icon i {
    font-size: 32px;
    color: #007bff;
    margin-bottom: 10px;
}

.category-icon h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

/* 内容区块 */
.section-block {
    margin-bottom: 50px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.05);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.section-header h2 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.section-header h2 i {
    color: #007bff;
    margin-right: 10px;
}

.more-link {
    color: #666;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.more-link:hover {
    color: #007bff;
    text-decoration: none;
}

/* 资讯卡片网格 */
.news-grid {
    margin: 0 -10px;
}

.news-grid .col-md-3 {
    padding: 0 10px;
    margin-bottom: 20px;
}

.news-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.news-image-wrapper {
    position: relative;
    padding-top: 66.67%; /* 3:2 比例 */
    overflow: hidden;
}

.news-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image {
    transform: scale(1.05);
}

.news-content {
    padding: 15px;
}

.news-title {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    height: 44px;
    overflow: hidden;
}

.news-title a {
    color: #333;
    text-decoration: none;
}

.news-title a:hover {
    color: #007bff;
}

.news-meta {
    color: #999;
    font-size: 12px;
}

.news-meta span {
    margin-right: 15px;
}

.news-meta i {
    margin-right: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .tourism-banner .carousel {
        height: 300px;
    }

    .category-icon {
        margin-bottom: 20px;
    }

    .section-block {
        margin-bottom: 30px;
        padding: 15px;
    }

    .section-header h2 {
        font-size: 20px;
    }
}

/* 资讯详情页 */
.tourism-detail-page {
    padding-top: 30px;
    padding-bottom: 50px;
    background-color: #fff;
}

.tourism-detail-page .page-header {
    text-align: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 30px;
}

.tourism-detail-page .page-header h1 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 15px;
}

.news-detail-meta {
    font-size: 14px;
    color: #999;
}

.news-detail-meta span {
    margin: 0 10px;
}

.news-detail-meta a {
    color: #999;
    text-decoration: none;
}

.news-detail-meta a:hover {
    color: #007bff;
    text-decoration: underline;
}

.news-detail-content {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
}

.news-detail-content img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
} 
<form id="config-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="vppz/config/edit">
    <div class="alert alert-info-light" style="margin-bottom:10px;">
        <b>温馨提示</b><br>
		修改配置后，需重新进入小程序端才会生效
       <!-- 2、小程序的AppID和AppSecret请在微信公众平台获取<br>-->
    </div>

    <div class="panel panel-default panel-intro">
        <div class="panel-heading">
            <ul class="nav nav-tabs nav-group">
                <li class="active"><a href="#all" data-toggle="tab">全部</a></li>
				<li><a href="#base" data-toggle="tab">基础配置</a></li>
				<li><a href="#wxapp" data-toggle="tab">小程序配置</a></li>
				<li><a href="#apis" data-toggle="tab">接口配置</a></li>
				<li><a href="#pay" data-toggle="tab">提现配置</a></li>
				<li><a href="#notify" data-toggle="tab">消息通知配置</a></li>
				<li><a href="#share" data-toggle="tab">转发配置</a></li>
				<li><a href="#other" data-toggle="tab">其他配置</a></li>

				<li class="pull-right"><a href="{:url('vppz/config/plugin?name=epay')}" title="支付配置" class="dialogit">支付配置</a></li>
				<li class="pull-right"><a href="{:url('vppz/config/plugin?name=address')}" title="地图配置" class="dialogit">地图配置</a></li>
            </ul>
        </div>

        <div class="panel-body">
            <div id="myTabContent" class="tab-content">
                <div class="tab-pane fade active in" id="one">
                    <table class="table table-striped table-config">
                        <tbody>
							<!--基础配置-->
							<tr data-type="base">
								<td width="15%">应用名称</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<input type="text" name="row[name]" value="{$datas.config['name']}" class="form-control" data-rule="required" data-tip="请填写应用名称"/>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="base">
								<td width="15%">应用LOGO</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="form-inline">
                                            <input id="c-logo" class="form-control" size="35" name="row[logo]" type="text" value="{$datas.config['logo']}" data-tip="请上传应用LOGO">
                                            <span><button type="button" id="plupload-logo" class="btn btn-danger plupload" data-input-id="c-logo" data-mimetype="image/*" data-multiple="false" data-preview-id="p-logo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-logo" class="btn btn-primary fachoose" data-input-id="c-logo" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                            <ul class="row list-inline plupload-preview" id="p-logo"></ul>
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="base">
								<td width="15%">底部LOGO</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="form-inline">
                                            <input id="c-logob" class="form-control" size="35" name="row[logob]" type="text" value="{$datas.config['logob']}" data-tip="请上传应用底部LOGO">
                                            <span><button type="button" id="plupload-logob" class="btn btn-danger plupload" data-input-id="c-logob" data-mimetype="image/*" data-multiple="false" data-preview-id="p-logob"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-logob" class="btn btn-primary fachoose" data-input-id="c-logob" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                            <ul class="row list-inline plupload-preview" id="p-logob"></ul>
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>


							<!--小程序配置-->
							<tr data-type="wxapp">
								<td width="15%">小程序AppID</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<input type="text" name="row[wxapp_id]" value="{$datas.config['wxapp_id']}" class="form-control" data-rule="required" data-tip="请填写小程序AppID"/>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="wxapp">
								<td width="15%">小程序AppSecret</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<input type="password" name="row[wxapp_secret]" value="{$datas.config['wxapp_secret']}" class="form-control" data-rule="required" data-tip="请填写小程序AppSecret"/>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>


							<!--接口配置-->
							<tr data-type="apis">
								<td width="15%">腾讯地图APPKEY</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[qmap_key]" value="{$datas.config['qmap_key']}" class="form-control"  data-tip="请填写腾讯地图APPKEY"/></div>
											<h6 class="text-muted">用于定位城市，前往腾讯地图注册申请</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<!--支付与提现配置-->
							<tr data-type="pay">
								<td width="15%">订单支付过期时效</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="number" name="row[pay_exp]" value="{$datas.config['pay_exp']}" class="form-control" data-rule="required" data-tip="请填写订单支付过期时效"/></div>
											<h6 class="text-muted">单位分钟，例：30，则用户下单30分钟内未支付则自动取消</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="pay">
								<td width="15%">提现方式</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="radio">
												<label for="row[outcash_channel]-wx"><input id="row[outcash_channel]-wx" name="row[outcash_channel]" type="radio" value="wx" {$datas.config['outcash_channel']=='wx'?'checked':''}/>微信号转账</label>
												<label for="row[outcash_channel]-ali"><input id="row[outcash_channel]-ali" name="row[outcash_channel]" type="radio" value="ali" {$datas.config['outcash_channel']=='ali'?'checked':''}/>支付宝账号转账</label>
												<label for="row[outcash_channel]-bank"><input id="row[outcash_channel]-bank" name="row[outcash_channel]" type="radio" value="bank" {$datas.config['outcash_channel']=='bank'?'checked':''}/>银行账号转账</label>
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="pay">
								<td width="15%">提现限制</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="input-group">
												<div class="input-group-addon">每次提现下限（元）</div>
												<input type="number" name="row[outcash_min]" class="form-control" value="{$datas.config['outcash_min']}" placeholder="例：100"/>
												<div class="input-group-addon">每次提现上限（元）</div>
												<input type="number" name="row[outcash_max]" class="form-control" value="{$datas.config['outcash_max']}" placeholder="例：2000" />
												<div class="input-group-addon">提现间隔时间（天）</div>
												<input type="number" name="row[outcash_sp]" class="form-control" value="{$datas.config['outcash_sp']}" placeholder="例：7" />
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>
							
							<!--消息通知配置-->
							<tr data-type="notify">
								<td width="15%">服务号AppId</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[mpappid]" value="{$datas.config['mpappid']}" class="form-control"  data-tip="请填写服务号AppId"/></div>
											<h6 class="text-muted">模板消息使用公众号模板消息发送，需将小程序与公众号关联，并在此除填入公众号的AppId</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="notify">
								<td width="15%">服务号Secret</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[mpappsecret]" value="{$datas.config['mpappsecret']}" class="form-control"  data-tip="请填写服务号Secret"/></div>
											<h6 class="text-muted">模板消息使用公众号模板消息发送，需将小程序与公众号关联，并在此除填入公众号的AppSecret</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="notify">
								<td width="15%">服务号二维码</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="form-inline">
                                            <input id="c-mpappqrcode" class="form-control" size="35" name="row[mpappqrcode]" type="text" value="{$datas.config['mpappqrcode']}" data-tip="请上传服务号二维码">
                                            <span><button type="button" id="plupload-mpappqrcode" class="btn btn-danger plupload" data-input-id="c-mpappqrcode" data-mimetype="image/*" data-multiple="false" data-preview-id="p-mpappqrcode"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-mpappqrcode" class="btn btn-primary fachoose" data-input-id="c-mpappqrcode" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                            <ul class="row list-inline plupload-preview" id="p-mpappqrcode"></ul>
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="notify">
								<td width="15%">有新订单通知接单</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[nt_order_new]" value="{$datas.config['nt_order_new']}" class="form-control"  data-tip="请填写模板消息ID"/></div>
											<h6 class="text-muted">请先绑定小程序与公众号，再进入公众号后台模板消息菜单添加模消息 ：  服务单进度提醒 - OPENTM411641845 - IT科技 - 互联网|电子商务</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>
							
							<!--转发配置-->
							<tr data-type="share">
								<td width="15%">默认转发标题</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[share_title]" value="{$datas.config['share_title']}" class="form-control"  data-tip="请填写默认转发标题"/></div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="share">
								<td width="15%">默认转发图片</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="form-inline">
                                            <input id="c-share_image" class="form-control" size="35" name="row[share_image]" type="text" value="{$datas.config['share_image']}" data-tip="请上传转发图片">
                                            <span><button type="button" id="plupload-share_image" class="btn btn-danger plupload" data-input-id="c-share_image" data-mimetype="image/*" data-multiple="false" data-preview-id="p-share_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-share_image" class="btn btn-primary fachoose" data-input-id="c-share_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                            <ul class="row list-inline plupload-preview" id="p-share_image"></ul>
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="share">
								<td width="15%">默认转发海报</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div class="form-inline">
                                            <input id="c-share_poster" class="form-control" size="35" name="row[share_poster]" type="text" value="{$datas.config['share_poster']}" data-tip="请上传海报图片">
                                            <span><button type="button" id="plupload-share_poster" class="btn btn-danger plupload" data-input-id="c-share_poster" data-mimetype="image/*" data-multiple="false" data-preview-id="p-share_poster"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-share_poster" class="btn btn-primary fachoose" data-input-id="c-share_poster" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                            <ul class="row list-inline plupload-preview" id="p-share_poster"></ul>
											</div>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>


							<!--其他配置-->
							<tr data-type="other">
								<td width="15%">常见问题页面地址</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[page_qa]" value="{$datas.config['page_qa']}" class="form-control"  data-tip="请填写常见问题页面地址"/></div>
											<h6 class="text-muted">填写方法：点此请前往[页面管理]功能中创建一个常见问题页面并填写相关内容，创建页面后将页面路径填回到此处</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="other">
								<td width="15%">推广说明页面地址</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[page_seller]" value="{$datas.config['page_seller']}" class="form-control"  data-tip="请填写推广说明页面地址"/></div>
											<h6 class="text-muted">填写方法：点此请前往[页面管理]功能中创建一个常见问题页面并填写相关内容，创建页面后将页面路径填回到此处</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="other">
								<td width="15%">用户协议页面地址</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[page_xy]" value="{$datas.config['page_xy']}" class="form-control"  data-tip="请填写用户协议页面地址"/></div>
											<h6 class="text-muted">填写方法：点此请前往[页面管理]功能中创建一个用户协议页面并填写相关内容，创建页面后将页面路径填回到此处</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>

							<tr data-type="other">
								<td width="15%">服务协议页面地址</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<div><input type="text" name="row[page_fw]" value="{$datas.config['page_fw']}" class="form-control"  data-tip="请填写服务协议页面地址"/></div>
											<h6 class="text-muted">填写方法：点此请前往[页面管理]功能中创建一个服务协议页面并填写相关内容，创建页面后将页面路径填回到此处</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>
							
							{if $VP_BRANCH==1}
							<tr data-type="other">
								<td width="15%">授权KEY</td>
								<td>
									<div class="row">
										<div class="col-sm-8 col-xs-12">
											<input type="text" name="row[vpkey]" value="{$datas.config['vpkey']}" class="form-control" data-rule="required" data-tip="请填写授权KEY"/>
											<h6 class="text-muted">请联系软件提供商获取授权</h6>
										</div>
										<div class="col-sm-4"></div>
									</div>
								</td>
							</tr>
							{/if}
						
                        </tbody>
                    </table>
                    <div class="form-group layer-footer">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-8">
                            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script>

    require.callback = function () {
        var tabevent = function () {
            $(document).on("click", ".nav-group li a[data-toggle='tab']", function () {
                var type = $(this).attr("href").substring(1);
                if (type == 'all') {
                    $(".table-config tr").show();
                } else {
                    $(".table-config tr").hide();
                    $(".table-config tr[data-type='" + type + "']").show();
                }
            });
        };


		define('backend/vppz/config', ['jquery', 'form'], function ($, Form) {
            var Controller = {
                index: function () {
                    Form.api.bindevent($("form[role=form]"));
                    tabevent();
                }
            };
            return Controller;
        });

    }

	/**
	require.callback = function () {

		var tabevent = function () {
			$(document).on("click", ".nav-group li a[data-toggle='tab']", function () {
				var type = $(this).attr("href").substring(1);
				if (type == 'all') {
					$(".table-config tr").show();
				} else {
					$(".table-config tr").hide();
					$(".table-config tr[data-type='" + type + "']").show();
				}
			});
		};

		define('backend/vppz/config', ['jquery', 'form'], function ($, Form) {
			var Controller = {
				index: function () {
					Form.api.bindevent($("form[role=form]"));
					tabevent();
				}
			};
			return Controller;
		});
	}
	**/


</script>

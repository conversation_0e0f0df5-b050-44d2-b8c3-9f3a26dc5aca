-- ----------------------------
-- Table structure for fa_tourism_category
-- ----------------------------
CREATE TABLE `fa_tourism_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '栏目名称',
  `icon` varchar(50) NOT NULL DEFAULT '' COMMENT '图标',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='旅游资讯栏目表';

-- ----------------------------
-- Table structure for fa_tourism_news
-- ----------------------------
CREATE TABLE `fa_tourism_news` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '栏目ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '封面图',
  `content` text COMMENT '内容',
  `views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='旅游资讯内容表';

-- ----------------------------
-- Records of fa_tourism_category
-- ----------------------------
INSERT INTO `fa_tourism_category` (`id`, `name`, `icon`, `description`, `weigh`, `status`, `createtime`, `updatetime`) VALUES
(1, '热门目的地', 'fa fa-map-marker', '国内外热门旅游目的地推荐', 1, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '旅游攻略', 'fa fa-compass', '详细的旅游规划与建议', 2, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '美食推荐', 'fa fa-cutlery', '当地特色美食与餐厅推荐', 3, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '文化探索', 'fa fa-university', '深度了解当地文化与历史', 4, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, '景点介绍', 'fa fa-camera', '著名景点与隐藏景点介绍', 5, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(6, '特色住宿', 'fa fa-bed', '特色民宿与酒店推荐', 6, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()); 
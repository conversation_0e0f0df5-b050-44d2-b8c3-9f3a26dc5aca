<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('App_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-app_id" data-rule="required" data-source="app/index" class="form-control selectpage" name="row[app_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Area_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="area/index" class="form-control selectpage" name="row[area_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Openid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-openid" data-rule="required" class="form-control" name="row[openid]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="required" class="form-control" size="50" name="row[avatar]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sex')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-sex" data-rule="required" class="form-control selectpicker" name="row[sex]">
                {foreach name="sexList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Age')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-age" data-rule="required" class="form-control" name="row[age]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" data-rule="required" class="form-control" name="row[mobile]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Realname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-realname" data-rule="required" class="form-control" name="row[realname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Idcardnum')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-idcardnum" data-rule="required" class="form-control" name="row[idcardnum]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Idcards_images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-idcards_images" data-rule="required" class="form-control" size="50" name="row[idcards_images]" type="textarea">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-idcards_images" class="btn btn-danger faupload" data-input-id="c-idcards_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-idcards_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-idcards_images" class="btn btn-primary fachoose" data-input-id="c-idcards_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-idcards_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-idcards_images"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Papers_images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-papers_images" data-rule="required" class="form-control" size="50" name="row[papers_images]" type="textarea">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-papers_images" class="btn btn-danger faupload" data-input-id="c-papers_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-papers_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-papers_images" class="btn btn-primary fachoose" data-input-id="c-papers_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-papers_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-papers_images"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Odmanar')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-odmanar" data-rule="required" class="form-control selectpicker" name="row[odmanar]">
                {foreach name="odmanarList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Master')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-master" data-rule="required" class="form-control selectpicker" name="row[master]">
                {foreach name="masterList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Master_uid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-master_uid" data-rule="required" class="form-control" name="row[master_uid]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Subs')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-subs" data-rule="required" class="form-control" name="row[subs]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Income')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-income" data-rule="required" class="form-control" step="0.01" name="row[income]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Income_service')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-income_service" data-rule="required" class="form-control" step="0.01" name="row[income_service]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Income_master')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-income_master" data-rule="required" class="form-control" step="0.01" name="row[income_master]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Outcash')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-outcash" data-rule="required" class="form-control" step="0.01" name="row[outcash]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[status_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-status_remark" data-rule="required" class="form-control " rows="5" name="row[status_remark]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" data-rule="required" class="form-control " rows="5" name="row[remark]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Use_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-use_switch" data-rule="required" class="form-control" name="row[use_switch]" type="number">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

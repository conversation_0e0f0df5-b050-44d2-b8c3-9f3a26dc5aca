define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/area_admin/index' + location.search,
                    //add_url: 'vppz/area_admin/add',
                    //edit_url: 'vppz/area_admin/edit',
                    del_url: 'vppz/area_admin/del',
                    multi_url: 'vppz/area_admin/multi',
                    import_url: 'vppz/area_admin/import',
                    table: 'vppz_area_admin',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url+'&area_id='+Config.area_id,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        //{field: 'id', title: __('Id')},
						
                        {field: 'area.id', title:'运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false,searchable:false},
						{field: 'area.name', title:'运营区', operate: 'LIKE',searchable:false},
						{field: 'admin_id', title:'管理员ID',visible: false},
						{field: 'admin.avatar', title: '管理员', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
                        {field: 'admin.username', title: __('Admin.username'), operate: 'LIKE'},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

			// 为表单绑定事件
			Form.api.bindevent($("form[role=form]"),function(data,ret){
				table.bootstrapTable('refresh', {});
				Toastr.success("添加成功");
				return false;
			});
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

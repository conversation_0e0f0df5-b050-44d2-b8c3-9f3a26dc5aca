<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class Hospital extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'vppz_hospital';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
		'avatar_url',	// 需在此处追加属性，才会触发获取器 getPicImageUrlAttr
		'type_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 关联医院类型
     */
    public function hospitaltype()
    {
        return $this->belongsTo('HospitalType', 'type_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        return isset($data['hospitaltype']) ? $data['hospitaltype']['name'] : '';
    }

	// 获取器直接转换url
	public function getAvatarUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['avatar']);
    }
}

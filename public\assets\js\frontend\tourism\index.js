define(['jquery', 'template'], function ($, Template) {
    var Controller = {
        index: function () {
            // 注册一个模板辅助函数，用于安全地截取和清理HTML内容
            Template.helper('getContentSummary', function (content) {
                if (!content) {
                    return '';
                }
                return content.replace(/<[^>]+>/g, "").substr(0, 100);
            });

            // 轮播图数据
            var banners = [
                {
                    image: '/uploads/tourism/banner1.jpg',
                    title: '探索世界的每一个角落',
                    url: '#'
                },
                {
                    image: '/uploads/tourism/banner2.jpg',
                    title: '发现最美的风景',
                    url: '#'
                },
                {
                    image: '/uploads/tourism/banner3.jpg',
                    title: '体验不一样的文化',
                    url: '#'
                }
            ];

            // 渲染轮播图
            var bannerHtml = '';
            banners.forEach(function(banner, index) {
                bannerHtml += `
                    <div class="item ${index === 0 ? 'active' : ''}" style="background-image: url('${banner.image}')">
                        <div class="carousel-caption">
                            <h3>${banner.title}</h3>
                        </div>
                    </div>
                `;
            });
            $('.carousel-inner').html(bannerHtml);

            // 获取栏目列表并渲染导航图标
            $.ajax({
                url: '/api/tourism/category/index',
                type: 'get',
                dataType: 'json',
                success: function (res) {
                    if (res.code === 1) {
                        var html = '';
                        res.data.forEach(function(category) {
                            html += `
                                <div class="col-md-2 col-sm-4 col-xs-6">
                                    <div class="category-icon" data-id="${category.id}">
                                        <i class="${category.icon}"></i>
                                        <h4>${category.name}</h4>
                                    </div>
                                </div>
                            `;
                        });
                        $('#category-icons').html(html);

                        // 加载各个栏目的内容
                        loadCategoryContent(1, '#destination-list');
                        loadCategoryContent(2, '#guide-list');
                        loadCategoryContent(3, '#food-list');
                        loadCategoryContent(4, '#culture-list');
                        loadCategoryContent(5, '#attraction-list');
                        loadCategoryContent(6, '#accommodation-list');
                    }
                }
            });

            // 加载栏目内容的函数
            function loadCategoryContent(categoryId, container) {
                $.ajax({
                    url: '/api/tourism/news/index',
                    type: 'get',
                    data: {
                        category_id: categoryId,
                        page: 1,
                        per_page: 4  // 每个栏目显示4条
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 1) {
                            var html = '';
                            res.data.data.forEach(function(item) {
                                html += `
                                    <div class="col-md-3 col-sm-6">
                                        <div class="news-card">
                                            <div class="news-image-wrapper">
                                                <a href="/index/tourism/news/detail/id/${item.id}.html">
                                                    <img class="news-image" src="${item.image}" alt="${item.title}">
                                                </a>
                                            </div>
                                            <div class="news-content">
                                                <h3 class="news-title">
                                                    <a href="/index/tourism/news/detail/id/${item.id}.html">${item.title}</a>
                                                </h3>
                                                <div class="news-meta">
                                                    <span><i class="fa fa-eye"></i> ${item.views}</span>
                                                    <span><i class="fa fa-clock-o"></i> ${item.createtime_text}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            $(container).html(html);
                        }
                    }
                });
            }

            // 点击栏目图标跳转到对应的栏目页面
            $(document).on('click', '.category-icon', function() {
                var categoryId = $(this).data('id');
                window.location.href = '/index/tourism/index?category_id=' + categoryId;
            });

            // 点击"更多"链接跳转到对应的栏目页面
            $(document).on('click', '.more-link', function(e) {
                e.preventDefault();
                var categoryId = $(this).data('category');
                window.location.href = '/index/tourism/index?category_id=' + categoryId;
            });
        }
    };
    return Controller;
}); 
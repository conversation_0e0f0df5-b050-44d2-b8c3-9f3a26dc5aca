define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/order_check/index' + location.search,
                    add_url: 'vppz/order_check/add',
                    edit_url: 'vppz/order_check/edit',
                    del_url: 'vppz/order_check/del',
                    multi_url: 'vppz/order_check/multi',
                    import_url: 'vppz/order_check/import',
                    table: 'vppz_order_check',
                }
            });

            var table = $("#table");


			var formatter_postion=function(value,row,index){
				return row.postion.district+row.postion.address;
			};


            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                //fixedColumns: true,
                //fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'area.id', title:'所属运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false},
						{field: 'area.name', title:'所属运营区', operate: 'LIKE',searchable:false},
                        {field: 'order.num', title:'订单号', operate: 'LIKE'},
						{field: 'order.service_name', title: __('Order.service_name'), operate: 'LIKE'},
                        {field: 'staff_id', title: '陪护师ID',visible: false},
                        {field: 'staff_uid', title: '陪护师UID',visible: false,searchable:false},
						{field: 'staff.avatar', title: '陪护师', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
                        {field: 'staff.nickname', title: '昵称', operate: 'LIKE'},

						{field: 'content', title: '记录内容',operate: 'LIKE',formatter:Table.api.formatter.content},

						{field: 'images_urls', title: '记录图片', formatter:Table.api.formatter.images,searchable:false},
						{field: 'postion', title: '记录地点', formatter:formatter_postion,searchable:false},

                        {field: 'createtime', title: '记录时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        //{field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

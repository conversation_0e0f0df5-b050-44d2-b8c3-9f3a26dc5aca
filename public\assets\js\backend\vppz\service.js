define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	$.validator.config({
		rules: {
			priceo: function (element) {
				if(parseFloat($('#c-priceo').val()) < parseInt($('#c-price').val())){
					return '划线价格不能低于当前价格';
				}
				return true
			}
		}
	});

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/service/index' + location.search,
                    add_url: 'vppz/service/add',
                    edit_url: 'vppz/service/edit',
                    del_url: 'vppz/service/del',
                    multi_url: 'vppz/service/multi',
                    import_url: 'vppz/service/import',
                    table: 'vppz_service',
                }
            });

            var table = $("#table");

			var formatter_path=function(value,row,index){
				return '/vp_pz/pages/index/service?svid='+row.id;
			};

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'stype', title: __('Stype'), searchList: {"10":__('Stype 10'),"15":__('Stype 15'),"20":__('Stype 20'),"30":__('Stype 30'),"40":__('Stype 40'),"110":__('Stype 110'),"210":__('Stype 210')}, formatter: Table.api.formatter.normal},
                        {field: 'name', title: '服务名称', operate: 'LIKE'},
						{field: 'code', title: '服务标识', operate: 'LIKE',visible: false},
                        {field: 'logo_image', title: '展示图片', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'icon_image', title: '服务图标', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image,visible: false},
						{field: 'tags', title: '分组标签', operate: 'LIKE'},
                        {field: 'price', title:'价格', operate:'BETWEEN'},
                        {field: 'priceo', title: '划线价', operate:'BETWEEN',visible: false},
						{field: 'path',title:'页面路径',formatter:formatter_path,searchable:false},
                        {field: 'use_switch', title: '是否上架', table: table, formatter: Table.api.formatter.toggle},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
                        //{field: 'admin_id', title: __('Admin_id')},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'vppz/service/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), align: 'left'},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '130px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'vppz/service/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'vppz/service/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所属运营区:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="vppz/area/index" class="form-control selectpage" name="row[area_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院形象照片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="required" class="form-control" size="50" name="row[avatar]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院等级:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rank" data-rule="required" class="form-control" name="row[rank]" type="text">
			<h6 class="text-muted">例：三甲</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院类型:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-label" class="form-control" name="row[label]" type="text" placeholder="医院类型标签（可选）">
			<h6 class="text-muted">例：综合病院</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院分类:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-type_id" class="form-control selectpicker" name="row[type_id]" data-rule="required">
                <option value="">请选择医院分类</option>
                {volist name="hospitalTypeList" id="vo" key="k"}
                <option value="{$k}">{$vo}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院简介:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-intro" data-rule="required" class="form-control " rows="5" name="row[intro]" cols="50"></textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所在地点:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[city]" type="text"></div>
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院位置:</label>
        <div class="col-xs-12 col-sm-8">
			<div class="input-group">
				<div class="input-group-addon">经度</div>
				<input id="c-lng" data-rule="required" class="form-control" name="row[lng]" type="number">
				<div class="input-group-addon">纬度</div>
                <input id="c-lat" data-rule="required" class="form-control"  name="row[lat]" type="number">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" class="btn btn-primary" data-input-id="c-address" data-toggle="addresspicker" data-lat-id="c-lat" data-lng-id="c-lng"><i class="fa fa-map-marker"></i> 选择位置</button></span>
                </div>
            </div>
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">详细地址:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">是否启用:</label>
        <div class="col-xs-12 col-sm-8">
			<input  id="c-use_switch" name="row[use_switch]" type="hidden" value="1">
			<a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-use_switch"  data-yes="1" data-no="0">
			<i class="fa fa-toggle-on text-success  fa-2x"></i>
			</a>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

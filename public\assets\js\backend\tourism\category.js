define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'tourism/category/index',
                    add_url: 'tourism/category/add',
                    edit_url: 'tourism/category/edit',
                    del_url: 'tourism/category/del',
                    multi_url: 'tourism/category/multi',
                    table: 'tourism_category',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name')},
                        {field: 'icon', title: __('Icon'), formatter: function(value, row, index) {
                            return '<i class="' + value + '"></i> ' + value;
                        }},
                        {field: 'description', title: __('Description')},
                        {field: 'weigh', title: __('Weigh')},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            // 绑定图标选择器
            $('#fa-icon-picker').on('click', function() {
                var that = this;
                var input = $(this).prev();
                parent.Fast.api.open("general/icon/select", "选择图标", {
                    callback: function(data) {
                        input.val('fa ' + data);
                    }
                });
            });
        },
        edit: function () {
            Controller.api.bindevent();
            // 绑定图标选择器
            $('#fa-icon-picker').on('click', function() {
                var that = this;
                var input = $(this).prev();
                parent.Fast.api.open("general/icon/select", "选择图标", {
                    callback: function(data) {
                        input.val('fa ' + data);
                    }
                });
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});

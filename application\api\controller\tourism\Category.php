<?php

namespace app\api\controller\tourism;

use app\common\controller\Api;

/**
 * 旅游资讯栏目接口
 */
class Category extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 栏目列表
     */
    public function index()
    {
        $list = \app\admin\model\tourism\Category::where('status', 'normal')
            ->order('weigh desc, id desc')
            ->select();
        $this->success('请求成功', $list);
    }
} 
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">提现者角色:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-biz" data-rule="required" class="form-control selectpicker" name="row[biz]" disabled>
                {foreach name="bizList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.biz"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">提现用户:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}" disabled>
        </div>
    </div>
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">联系电话:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" data-rule="required" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}" disabled>
        </div>
    </div>
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">实际提现金额</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cash" data-rule="required" class="form-control" step="0.01" name="row[cash]" type="number" value="{$row.cash|htmlentities}" disabled>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Channel')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-channel" class="form-control selectpicker" name="row[channel]" disabled>
                {foreach name="channelList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.channel"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">账户名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel_name" class="form-control" name="row[channel_name]" type="text" value="{$row.channel_name|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">收款账号:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel_account" class="form-control" name="row[channel_account]" type="text" value="{$row.channel_account|htmlentities}" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">真实姓名:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-channel_realname" class="form-control" name="row[channel_realname]" type="text" value="{$row.channel_realname|htmlentities}" disabled>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fedback')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-fedback" class="form-control " rows="5" name="row[fedback]" cols="50">{$row.fedback|htmlentities}</textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control " rows="5" name="row[remark]" cols="50">{$row.remark|htmlentities}</textarea>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
